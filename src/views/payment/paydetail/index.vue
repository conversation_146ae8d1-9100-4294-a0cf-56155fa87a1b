<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="玩家ID" prop="uid">
            <el-input
              v-model="queryParams.uid"
              placeholder="请输入玩家ID"
              clearable
              size="small"
              style="width: 200px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="订单状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="订单状态"
              clearable
              size="small"
              style="width: 160px"
            >
              <el-option
                v-for="dict in statusOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="dateRange"
              size="small"
              type="datetimerange"
              :picker-options="pickerOptions"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              align="right"
              value-format="yyyy-MM-dd HH:mm:ss"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <el-button
              v-permisaction="['admin:sysOperLog:export']"
              type="warning"
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
            >导出</el-button>
          </el-form-item>
        </el-form>
        <el-table v-loading="loading" :data="list" border @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="40" align="center" />
          <el-table-column label="订单号" width="120" prop="order_id" align="center" />
          <el-table-column label="玩家ID" width="80" prop="uid" align="center" />
          <el-table-column
            label="支付渠道"
            prop="pay_channel"
            width="140"
            align="center"
            :formatter="payChannelFormat"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              {{ payChannelFormat(scope.row.pay_channel) }}
              <el-badge v-if="scope.row.is_test==true">(测试)</el-badge>
            </template> </el-table-column>
          <el-table-column
            label="金额￠"
            prop="price_in_cent"
            width="80"
            align="center"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="支付ID"
            prop="purchase_id"
            width="80"
            align="center"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="订单状态"
            prop="order_status"
            width="100"
            align="center"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <!-- <el-tag v-if="scope.row.order_status==6" type="warning"> {{ statusFormat(scope.row, scope.row.order_status) }} </el-tag> -->
              <el-tag v-if="scope.row.order_status==12" type="danger"> {{ statusFormat(scope.row, scope.row.order_status) }} </el-tag>
              <el-tag v-if="scope.row.order_status==10" type="success"> {{ statusFormat(scope.row, scope.row.order_status) }} </el-tag>
              <el-tag v-if="scope.row.order_status==9" type="success"> {{ statusFormat(scope.row, scope.row.order_status) }} </el-tag>
              <el-tag v-if="scope.row.order_status==8" type="danger"> {{ statusFormat(scope.row, scope.row.order_status) }} </el-tag>
              <el-tag v-if="scope.row.order_status==7"> {{ statusFormat(scope.row, scope.row.order_status) }} </el-tag>
              <el-tag v-if="scope.row.order_status==6" type="warning"> {{ statusFormat(scope.row, scope.row.order_status) }} </el-tag>
              <el-tag v-if="scope.row.order_status==5" type="danger"> {{ statusFormat(scope.row, scope.row.order_status) }} </el-tag>
              <el-tag v-if="scope.row.order_status==4"> {{ statusFormat(scope.row, scope.row.order_status) }} </el-tag>
              <el-tag v-if="scope.row.order_status==3"> {{ statusFormat(scope.row, scope.row.order_status) }} </el-tag>
              <el-tag v-if="scope.row.order_status==2" type="info"> {{ statusFormat(scope.row, scope.row.order_status) }} </el-tag>
              <el-tag v-if="scope.row.order_status==1"> {{ statusFormat(scope.row, scope.row.order_status) }} </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="支付入口"
            prop="trigger_type"
            width="120"
            align="center"
            :formatter="payChannelFormat"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              {{ triggerTypeFormat(scope.row, scope.row.pay_channel) }}
            </template> </el-table-column>
          <el-table-column
            label="ProductID"
            prop="product_id"
            width="280"
            align="left"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="创建时间" prop="create_stamp" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.create_stamp) }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column label="测试订单" prop="is_test" width="180">
            <template slot-scope="scope">
              <div>{{ scope.row.is_test }}</div>
            </template>
          </el-table-column> -->
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                v-permisaction="['payment:list:query']"
                size="mini"
                type="text"
                icon="el-icon-view"
                @click="handleView(scope.row,scope.index)"
              >详细</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

        <!-- 详细 -->
        <el-dialog title="订单详情" :visible.sync="open" width="700px">
          <el-form ref="form" :model="form" label-width="100px" size="mini">
            <el-row>
              <el-col :span="8" align="left">
                <el-form-item label="玩家ID：">{{ form.uid }}</el-form-item>
              </el-col>
              <el-col :span="8" align="left">
                <el-form-item label="订单ID：">{{ form.order_id }}</el-form-item>
              </el-col>
              <el-col :span="8" align="left">
                <el-form-item label="支付ID：">{{ form.purchase_id }}</el-form-item>
              </el-col>
              <el-divider />
              <el-col :span="8" align="left">
                <el-form-item label="金额：">{{ form.price_in_cent }}￠</el-form-item>
              </el-col>
              <el-col :span="8" align="left">
                <el-form-item label="支付渠道：">{{ form.pay_channel }}</el-form-item>
              </el-col>
              <el-col :span="8" align="left">
                <el-form-item label="订单状态：">
                  <el-tag v-if="form.order_status==12" type="warning"> {{ statusFormat(form, form.order_status) }} </el-tag>
                  <el-tag v-if="form.order_status==10" type="success"> {{ statusFormat(form, form.order_status) }} </el-tag>
                  <el-tag v-if="form.order_status==9" type="success"> {{ statusFormat(form, form.order_status) }} </el-tag>
                  <el-tag v-if="form.order_status==8" type="danger"> {{ statusFormat(form, form.order_status) }} </el-tag>
                  <el-tag v-if="form.order_status==7"> {{ statusFormat(form, form.order_status) }} </el-tag>
                  <el-tag v-if="form.order_status==6" type="warning"> {{ statusFormat(form, form.order_status) }} </el-tag>
                  <el-tag v-if="form.order_status==5" type="danger"> {{ statusFormat(form, form.order_status) }} </el-tag>
                  <el-tag v-if="form.order_status==4" type="success"> {{ statusFormat(form, form.order_status) }} </el-tag>
                  <el-tag v-if="form.order_status==2" type="info"> {{ statusFormat(form, form.order_status) }} </el-tag>
                  <el-tag v-if="form.order_status==1"> {{ statusFormat(form, form.order_status) }} </el-tag>
                </el-form-item>
              </el-col>
              <el-col :span="8" align="left">
                <el-form-item label="支付入口："> {{ form.entrance_type }}</el-form-item>
              </el-col>
              <el-col :span="8" align="left">
                <el-form-item label="触发入口：">{{ triggerTypeFormat(form, form.trigger_type) }}</el-form-item>
              </el-col>
              <el-col :span="8" align="left">
                <el-form-item label="设备平台：">{{ platformFormat(form, form.platform) }}</el-form-item>
              </el-col>
              <el-col :span="24" align="left">
                <el-form-item label="ProductId：">{{ form.product_id }}</el-form-item>
              </el-col>
              <el-col :span="12" align="left">
                <el-form-item label="创建时间：">{{ parseTime(form.create_stamp) }}</el-form-item>
              </el-col>
              <el-col :span="12" align="left">
                <el-form-item label="时间戳：">{{ parseTime(form.time_stamp) }}</el-form-item>
              </el-col>
              <el-col :span="12" align="left">
                <el-form-item label="失败类型：">{{ form.fail_type }}</el-form-item>
              </el-col>
              <el-col :span="24" align="left">
                <el-form-item label="奖励Json：">{{ form.reword_json }}</el-form-item>
              </el-col>
              <el-col :span="24" align="left">
                <el-form-item label="凭证：">{{ form.receipt }}</el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="open = false">关 闭</el-button>
          </div>
        </el-dialog>
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { listOrder, cleanOperlog } from '@/api/payment/orderinfo'
import { formatJson } from '@/utils'

export default {
  name: 'PaymentManage',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      // 是否显示弹出层
      open: false,
      // 类型数据字典
      statusOptions: [],
      // 支付渠道字典
      payChannelOptions: [],
      // 支付入口类型字典
      triggerTypeOptions: [],
      // 平台类型字典
      platformOptions: [],
      pickerOptions: {},
      // 日期范围
      dateRange: [],
      // 表单参数
      form: {},
      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        uid: undefined,
        operName: undefined,
        businessType: undefined,
        status: undefined,
        createdAtOrder: 'desc'
      }
    }
  },
  created() {
    this.getList()

    this.getDicts('order_status').then(response => {
      this.statusOptions = response.data
    })

    this.getDicts('trigger_type').then(response => {
      this.triggerTypeOptions = response.data
    })

    this.getDicts('platform').then(response => {
      this.platformOptions = response.data
    })

    this.getDicts('pay_channel').then((response) => {
      this.payChannelOptions = response.data
    })
  },
  methods: {
    getList() {
      this.loading = true
      listOrder(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        console.log(response.data.list)
        this.list = response.data.list
        this.total = response.data.count
        this.loading = false
      }
      )
    },
    // 操作日志状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.statusOptions, row.order_status)
    },
    payChannelFormat(payChannel) {
      return this.selectDictLabel(this.payChannelOptions, payChannel)
    },
    triggerTypeFormat(row, column) {
      return this.selectDictLabel(this.triggerTypeOptions, row.trigger_type)
    },
    platformFormat(row, column) {
      return this.selectDictLabel(this.platformOptions, row.platform)
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.multiple = !selection.length
    },
    /** 详细按钮操作 */
    handleView(row) {
      this.open = true
      this.form = row
    },
    /** 清空按钮操作 */
    handleClean() {
      this.$confirm('是否确认清空所有操作日志数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return cleanOperlog()
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function() {})
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams
      var query = this.addDateRange(queryParams, this.dateRange)
      console.log(query)
      if ((query.beginTime !== '' && query.beginTime != null) && (query.endTime !== '' && query.endTime != null)) {
        console.log(this.dateRange)
      } else {
        this.msgError('请选择时间区间(导出数据跟当前检索条件保持一致)')
        return
      }

      this.loading = true
      listOrder(query).then(response => {
        var total = response.data.count
        this.loading = false

        this.$confirm('是否确认导出数据项?共(' + total + ')条', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.downloadLoading = true
          queryParams.pageIndex = 1
          queryParams.pageSize = total
          console.log(queryParams.pageSize)
          listOrder(this.addDateRange(queryParams, this.dateRange)).then(response => {
            import('@/vendor/Export2Excel').then(excel => {
              const tHeader = ['订单号', '用户ID', '支付渠道', 'ProductID', '金额(美分)', '支付ID', '订单状态', '订单创建时间', '时间戳', 'entrance_type', 'fail_type', 'is_test', 'trigger_type', 'transaction_id', 'reword_json']
              const filterVal = ['order_id', 'uid', 'pay_channel', 'product_id', 'price_in_cent', 'purchase_id', 'order_status', 'create_stamp', 'time_stamp', 'entrance_type', 'fail_type', 'is_test', 'trigger_type', 'transaction_id', 'reword_json']
              const data = formatJson(filterVal, response.data.list)
              excel.export_json_to_excel({
                header: tHeader,
                data,
                filename: '支付订单一览',
                autoWidth: true, // Optional
                bookType: 'xlsx' // Optional
              })
              this.downloadLoading = false
            })
          })
        })
      }
      )
    }
  }
}
</script>

