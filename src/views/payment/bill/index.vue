
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form
          ref="queryForm"
          :model="queryParams"
          :inline="true"
          label-width="68px"
        >
          <el-form-item
            label="玩家ID"
            prop="uid"
          ><el-input
            v-model="queryParams.uid"
            placeholder="请输入玩家ID"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="订单号" prop="orderId"><el-input
            v-model="queryParams.orderId"
            placeholder="请输入订单号"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item
            label="支付渠道"
            prop="payChannel"
          ><el-select
            v-model="queryParams.payChannel"
            placeholder="支付渠道"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in payChannelOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          </el-form-item>
          <el-form-item
            label="订单状态"
            prop="orderStatus"
          ><el-select
            v-model="queryParams.orderStatus"
            placeholder="订单状态"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in orderStatusOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
            >搜索</el-button>
            <el-button
              icon="el-icon-refresh"
              size="mini"
              @click="resetQuery"
            >重置</el-button>
            <el-button
              v-permisaction="['admin:sysOperLog:export']"
              type="warning"
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
            >导出</el-button>
          </el-form-item>
        </el-form>
        <el-table
          v-loading="loading"
          :data="tOrderBillLogList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            width="55"
            align="center"
          /><el-table-column
            label="订单号"
            align="left"
            prop="orderId"
            :show-overflow-tooltip="true"
            width="120"
          /><el-table-column
            label="玩家ID"
            align="left"
            prop="uid"
            :show-overflow-tooltip="true"
            width="100"
          /><el-table-column
            label="支付渠道"
            align="center"
            prop="payChannel"
            :formatter="payChannelFormat"
            width="120"
          >
            <template slot-scope="scope">
              {{ payChannelFormat(scope.row.payChannel) }}
            </template> </el-table-column><el-table-column
            label="订单状态"
            align="center"
            prop="orderStatus"
            :formatter="orderStatusFormat"
            width="120"
          >
            <template slot-scope="scope">
              <el-tag v-if="scope.row.orderStatus==12" type="danger"> {{ orderStatusFormat(scope.row, scope.row.orderStatus) }} </el-tag>
              <el-tag v-if="scope.row.orderStatus==10" type="success"> {{ orderStatusFormat(scope.row, scope.row.orderStatus) }} </el-tag>
              <el-tag v-if="scope.row.orderStatus==9" type="success"> {{ orderStatusFormat(scope.row, scope.row.orderStatus) }} </el-tag>
              <el-tag v-if="scope.row.orderStatus==8" type="info"> {{ orderStatusFormat(scope.row, scope.row.orderStatus) }} </el-tag>
              <el-tag v-if="scope.row.orderStatus==7"> {{ orderStatusFormat(scope.row, scope.row.orderStatus) }} </el-tag>
              <el-tag v-if="scope.row.orderStatus==6" type="warning"> {{ orderStatusFormat(scope.row, scope.row.orderStatus) }} </el-tag>
              <el-tag v-if="scope.row.orderStatus==5" type="danger"> {{ orderStatusFormat(scope.row, scope.row.orderStatus) }} </el-tag>
              <el-tag v-if="scope.row.orderStatus==4"> {{ orderStatusFormat(scope.row, scope.row.orderStatus) }} </el-tag>
              <el-tag v-if="scope.row.orderStatus==3"> {{ orderStatusFormat(scope.row, scope.row.orderStatus) }} </el-tag>
              <el-tag v-if="scope.row.orderStatus==2" type="info"> {{ orderStatusFormat(scope.row, scope.row.orderStatus) }} </el-tag>
              <el-tag v-if="scope.row.orderStatus==1"> {{ orderStatusFormat(scope.row, scope.row.orderStatus) }} </el-tag>
            </template> </el-table-column>
          <el-table-column
            label="支付状态"
            align="center"
            prop="sdkOrderStatus"
            :show-overflow-tooltip="true"
            width="120"
          /><el-table-column
            label="消费状态"
            align="center"
            prop="sdkConsumptionState"
            :show-overflow-tooltip="true"
            width="120"
          /><el-table-column
            label="处理错误码"
            align="center"
            prop="errorCode"
            width="120"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="时间戳"
            align="center"
            prop="timeStamp"
            :show-overflow-tooltip="true"
            width="150"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.timeStamp) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                v-permisaction="['payment:list:query']"
                size="mini"
                type="text"
                icon="el-icon-view"
                @click="handleView(scope.row,scope.index)"
              >详细</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
        <!-- 详细 -->
        <el-dialog title="发货流水详情" :visible.sync="open" width="700px">
          <el-form ref="form" :model="form" label-width="100px" size="mini">
            <el-row>
              <el-col :span="6" align="left">
                <el-form-item label="玩家ID：">{{ form.uid }}</el-form-item>
              </el-col>
              <el-col :span="6" align="left">
                <el-form-item label="订单ID：">{{ form.orderId }}</el-form-item>
              </el-col>
              <el-col :span="12" align="left">
                <el-form-item label="时间戳：">{{ parseTime(form.timeStamp) }}</el-form-item>
              </el-col>
              <el-divider />

              <el-col :span="12" align="left">
                <el-form-item label="支付渠道：" :formatter="payChannelFormat">
                  {{ payChannelFormat(form.payChannel) }}
                </el-form-item>
              </el-col>
              <el-col :span="12" align="left">
                <el-form-item label="订单状态：">
                  <el-tag v-if="form.orderStatus==12" type="danger"> {{ orderStatusFormat(form, form.orderStatus) }} </el-tag>
                  <el-tag v-if="form.orderStatus==10" type="success"> {{ orderStatusFormat(form, form.orderStatus) }} </el-tag>
                  <el-tag v-if="form.orderStatus==9" type="success"> {{ orderStatusFormat(form, form.orderStatus) }} </el-tag>
                  <el-tag v-if="form.orderStatus==8" type="danger"> {{ orderStatusFormat(form, form.orderStatus) }} </el-tag>
                  <el-tag v-if="form.orderStatus==7"> {{ orderStatusFormat(form, form.orderStatus) }} </el-tag>
                  <el-tag v-if="form.orderStatus==6" type="warning"> {{ orderStatusFormat(form, form.orderStatus) }} </el-tag>
                  <el-tag v-if="form.orderStatus==5" type="danger"> {{ orderStatusFormat(form, form.orderStatus) }} </el-tag>
                  <el-tag v-if="form.orderStatus==4"> {{ orderStatusFormat(form, form.orderStatus) }} </el-tag>
                  <el-tag v-if="form.orderStatus==3"> {{ orderStatusFormat(form, form.orderStatus) }} </el-tag>
                  <el-tag v-if="form.orderStatus==2" type="info"> {{ orderStatusFormat(form, form.orderStatus) }} </el-tag>
                  <el-tag v-if="form.orderStatus==1"> {{ orderStatusFormat(form, form.orderStatus) }} </el-tag>
                </el-form-item>
              </el-col>
              <el-col :span="24" align="left">
                <el-form-item label="处理结果：">
                  {{ form.errorCode }} : {{ form.errorMsg }}
                </el-form-item>
              </el-col>
              <el-col :span="24" align="left">
                <el-form-item label="校验结果：" />
              </el-col>
              <el-col :span="12" align="left">
                <el-form-item label="支付状态：">{{ form.sdkOrderStatus }}</el-form-item>
              </el-col>
              <el-col :span="12" align="left">
                <el-form-item label="消费状态">{{ form.sdkConsumptionState }}</el-form-item>
              </el-col>
              <el-col :span="24" align="left">
                <el-form-item label="返回内容："><pre> {{ jsonView(form.dealRet) }}</pre></el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="open = false">关 闭</el-button>
          </div>
        </el-dialog>
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { listTOrderBillLog } from '@/api/payment/paymentbill'
import { formatJson } from '@/utils'

export default {
  name: 'TOrderBillLog',
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      tOrderBillLogList: [],
      payChannelOptions: [],
      orderStatusOptions: [],
      // 关系表类型

      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        orderId: undefined,
        uid: undefined,
        payChannel: undefined,
        orderStatus: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        orderId: [
          { required: true, message: '订单号不能为空', trigger: 'blur' }
        ],
        uid: [{ required: true, message: '玩家ID不能为空', trigger: 'blur' }],
        payChannel: [
          { required: true, message: '支付渠道不能为空', trigger: 'blur' }
        ],
        orderStatus: [
          { required: true, message: '订单状态不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
    this.getDicts('pay_channel').then((response) => {
      this.payChannelOptions = response.data
    })
    this.getDicts('order_status').then((response) => {
      this.orderStatusOptions = response.data
    })
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      listTOrderBillLog(
        this.addDateRange(this.queryParams, this.dateRange)
      ).then((response) => {
        this.tOrderBillLogList = response.data.list
        this.total = response.data.count
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        orderId: undefined,
        uid: undefined,
        payChannel: undefined,
        reqUrl: undefined,
        orderStatus: undefined,
        sdkOrderStatus: undefined,
        sdkConsumptionState: undefined,
        errorCode: undefined,
        errorMsg: undefined,
        dealRet: undefined,
        timeStamp: undefined
      }
      this.resetForm('form')
    },
    getImgList: function() {
      this.form[this.fileIndex] =
        this.$refs['fileChoose'].resultList[0].fullUrl
    },
    fileClose: function() {
      this.fileOpen = false
    },
    payChannelFormat(payChannel) {
      return this.selectDictLabel(this.payChannelOptions, payChannel)
    },
    orderStatusFormat(row) {
      return this.selectDictLabel(this.orderStatusOptions, row.orderStatus)
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加TOrderBillLog'
      this.isEdit = false
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 详细按钮操作 */
    handleView(row) {
      this.open = true
      this.form = row
    },
    /** Json格式化 */
    jsonView(str) {
      if (str == null || str.length === 0) {
        return ''
      } else {
        return JSON.parse(str)
      }
    },
    /** 导出按钮操作 */
    handleExport() {
      this.loading = true
      const queryParams = this.queryParams
      listTOrderBillLog(
        this.addDateRange(this.queryParams, this.dateRange)
      ).then((response) => {
        var total = response.data.count
        this.$confirm('是否确认导出数据项?共(' + total + ')条', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.loading = true
          queryParams.pageIndex = 1
          queryParams.pageSize = total
          listTOrderBillLog(
            this.addDateRange(this.queryParams, this.dateRange)
          ).then((response) => {
            this.tOrderBillLogList = response.data.list
            this.loading = false

            this.downloadLoading = true
            import('@/vendor/Export2Excel').then(excel => {
              const tHeader = ['订单号', '用户ID', '支付渠道', '订单状态', 'SDK订单状态', 'SdkConsumptionState', '错误码', '错误内容', 'ReqUrl', '处理结果', '时间戳']
              const filterVal = ['orderId', 'uid', 'payChannel', 'orderStatus', 'sdkOrderStatus', 'sdkConsumptionState', 'errorCode', 'errorMsg', 'reqUrl', 'dealRet', 'time_stamp']
              const list = this.tOrderBillLogList
              const data = formatJson(filterVal, list)
              console.log(data)
              excel.export_json_to_excel({
                header: tHeader,
                data,
                filename: '支付发货记录',
                autoWidth: true, // Optional
                bookType: 'xlsx' // Optional
              })
              this.downloadLoading = false
            })
          })
        })
      })
    }
  }
}
</script>
