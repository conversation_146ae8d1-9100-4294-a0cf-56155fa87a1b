
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-alert
          title="⚠️请仔细确认邮件内容准确无误。"
          type="warning"
          show-icon
        />
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="标题" prop="mailTitle"><el-input
            v-model="queryParams.mailTitle"
            placeholder="请输入标题"
            clearable
            size="mini"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <!-- <el-form-item label="内容" prop="mailDetail"><el-input
            v-model="queryParams.mailDetail"
            placeholder="请输入内容"
            clearable
            size="mini"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item> -->
          <el-form-item label="标签" prop="mailUserTag"><el-input
            v-model="queryParams.mailUserTag"
            placeholder="请输入标签"
            clearable
            size="mini"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="邮件类型" prop="mailType"><el-select
            v-model="queryParams.mailType"
            placeholder="邮件类型"
            clearable
            size="mini"
          >
            <el-option
              v-for="dict in mailTypeOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <el-button
              v-permisaction="['mail:tMailMaster:add']"
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
            >新增
            </el-button>
          </el-form-item>
        </el-form>

        <el-table v-loading="loading" :data="tMailMasterList" @selection-change="handleSelectionChange">
          <el-table-column type="index" label="序号" width="55" align="center" />
          <el-table-column
            label="标题"
            align="center"
            prop="mailTitle"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="内容"
            align="center"
            prop="mailDetail"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="邮件类型"
            align="center"
            prop="mailType"
            :formatter="mailTypeFormat"
            width="100"
          >
            <template slot-scope="scope">
              {{ mailTypeFormat(scope.row) }}
            </template>
          </el-table-column><el-table-column
            label="邮件创建时间"
            align="center"
            prop="mailCreatedAt"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.mailCreatedAt) }}</span>
            </template>
          </el-table-column><el-table-column
            label="邮件截至时间"
            align="center"
            prop="mailEndAt"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.mailEndAt) }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column
            label="邮件动作"
            align="center"
            prop="mailActive"
            :formatter="mailActiveFormat"
            width="100"
          >
            <template slot-scope="scope">
              {{ mailActiveFormat(scope.row) }}
            </template>
          </el-table-column> -->
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                slot="reference"
                v-permisaction="['mail:tMailMaster:edit']"
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
              >查看
              </el-button>
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要撤销吗?"
                confirm-button-text="撤销"
                @onConfirm="handleDelete(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['mail:tMailMaster:remove']"
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                >撤销
                </el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="title" :visible.sync="open" :close-on-click-modal="false" width="960px">
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">
            <el-row :gutter="24">
              <el-card class="box-card">
                <el-alert
                  title="⚠️请确认邮件内容的国际化，点击按钮添加多个语言。"
                  type="warning"
                  show-icon
                />
                <div slot="header">
                  <span>发送内容</span>
                  <el-button type="warning" :disabled="isEdit" style="float: right; padding: 4px" size="mini" icon="el-icon-plus" @click="addContext">添加语言</el-button>
                </div>
                <el-card class="box-card-sub">
                  <el-form-item
                    v-for="(poc, index) in form.contexts"
                    :key="poc.key"
                    align="right"
                    :label="'语言' + (index + 1)"
                    :prop="'contexts.' + index + '.context'"
                    :rules="{
                      required: true, message: '请填写正确信息', trigger: 'blur'
                    }"
                  >
                    <el-col :span="5">
                      <el-select
                        v-model="poc.lang"
                        placeholder="选择语言"
                        clearable
                        :disabled="isEdit"
                        @change="selectLangItem($event, poc)"
                      >
                        <el-option
                          v-for="dict in languageTypeOptions"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        />
                      </el-select></el-col>
                    <el-col :span="15">
                      <el-input
                        v-model="poc.title"
                        :disabled="isEdit"
                        placeholder="请输入邮件标题"
                      />
                    </el-col>
                    <el-col :span="4">
                      <el-button type="danger" :disabled="isEdit" size="mini" icon="el-icon-delete" @click.prevent="removeContext(poc)">删除</el-button>
                    </el-col>
                    <el-col :span="24">
                      <el-input
                        v-model="poc.context"
                        type="textarea"
                        rows="2"
                        :disabled="isEdit"
                        placeholder="内容(支持前端富文本标签)"
                        border
                      />
                    </el-col>
                  </el-form-item>
                </el-card>
              </el-card>
            </el-row>
            <el-row :gutter="24">
              <el-card class="box-card">
                <div slot="header">
                  <span>奖励</span>
                  <el-button type="warning" :disabled="isEdit" style="float: right; padding: 4px" size="mini" icon="el-icon-plus" @click="addProp">添加奖励</el-button>
                </div>
                <el-form-item
                  v-for="(po, index) in form.props"
                  :key="po.key"
                  align="right"
                  :label="'道具' + (index + 1)"
                  :prop="'props.' + index + '.DeltaCount'"
                  :rules="{
                    required: true, message: '请填写正确物品信息', trigger: 'blur'
                  }"
                >
                  <el-col :span="6">
                    <el-select
                      v-model="po.PropType"
                      placeholder="选择物品"
                      clearable
                      :disabled="isEdit"
                      @change="selectItem($event, po)"
                    >
                      <el-option
                        v-for="dict in propTypeOptions"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select></el-col>
                  <el-col :span="6">
                    <el-input v-model="po.propId" placeholder="输入PropID" :disabled="po.showPropID===false" />
                  </el-col>
                  <el-col :span="6">
                    <el-input-number v-model="po.DeltaCount" :disabled="isEdit" :min="1" label="数量" />
                  </el-col>
                  <el-col :span="4">
                    <el-button type="danger" :disabled="isEdit" size="mini" icon="el-icon-delete" @click.prevent="removeProp(po)">删除</el-button>
                  </el-col>
                </el-form-item>
              </el-card>
              <el-card class="box-card">
                <el-form-item label="标签" :disabled="isEdit" prop="mailUserTag">
                  <el-input
                    v-model="form.mailUserTag"
                    :disabled="isEdit"
                    placeholder="标签（多个以竖线分割，eg:UserLabel1|UserLabel2）"
                  />
                </el-form-item>
                <div style="margin-top: 15px;">
                  <el-form-item label="发送给" prop="mailDest">
                    <el-input v-model="form.mailDest" :disabled="isEdit" placeholder="玩家ID（多个以竖线分割，eg:10001|10005）">
                      <template slot="append">
                        <el-tooltip class="item" content="批量导入UserID，请确认是真实存在的UID" effect="light" placement="bottom" ali>
                          <el-button
                            v-permisaction="['config:configFieldMaster:add']"
                            type="primary"
                            :disabled="isEdit"
                            icon="el-icon-upload"
                            size="mini"
                            @click="handleImport"
                          >批量导入玩家
                          </el-button>
                        </el-tooltip>
                      </template>
                    </el-input>
                  </el-form-item>
                </div>
                <el-row :gutter="20">
                  <el-col :span="10">
                    <el-form-item label="邮件类型" prop="mailType">
                      <el-select
                        v-model="form.mailType"
                        :disabled="isEdit"
                        placeholder="选择邮件类型"
                        size="small"
                      >
                        <el-option
                          v-for="dict in mailTypeOptions"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value+''"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="10">
                    <div class="block">
                      <el-date-picker
                        v-model="dateRange"
                        type="datetimerange"
                        align="right"
                        :disabled="isEdit"
                        unlink-panels
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        :picker-options="pickerOptions2"
                      />
                    </div>
                  </el-col>
                </el-row>
                <el-form-item label="附加参数" prop="mailActiveParam">
                  <el-input
                    v-model="form.mailActiveParam"
                    :disabled="isEdit"
                    placeholder="操作附带参数"
                  />
                </el-form-item>
                <el-form-item label="备注" prop="mailRemark">
                  <el-input
                    v-model="form.mailRemark"
                    :disabled="isEdit"
                    placeholder="备注"
                  />
                </el-form-item>
              </el-card>
            </el-row>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" :disabled="isEdit" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>

        <!-- 文件导入后对话框 -->
        <el-dialog :title="title" :visible.sync="open2" width="820px">
          <el-form ref="form" :model="formImport" :rules="rules" label-width="80px">
            <el-alert
              title="⚠️请确认玩家ID是否正确"
              type="warning"
              show-icon
            />
            <el-form-item label="玩家ID" prop="field"><el-input
              v-model="formImport.uid"
              type="textarea"
              placeholder="请输入字段"
              size="small"
              readonly
              rows="20"
              @keyup.enter.native="handleQuery"
            />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="handleClickUseID">确 定</el-button>
            <el-button @click="cancel2">取 消</el-button>
          </div>
        </el-dialog>
        <!-- 用户导入对话框 -->
        <el-dialog :title="uploadFrame.title" :visible.sync="uploadFrame.open" width="400px">
          <div>
            <!-- <input ref="excel-upload-input" class="excel-upload-input" type="file" accept=".xlsx, .xls" @change="handleClick"> -->
            <div align="center">
              <input ref="excel-upload-input" class="excel-upload-input" type="file" accept=".xlsx, .xls" @change="handleClick">
            </div>
            <div class="drop" align="center" @drop="handleDrop" @dragover="handleDragover" @dragenter="handleDragover">
              <!-- Drop excel file here or -->
              <!-- <el-button :loading="loading" style="margin-left:16px;" size="mini" type="primary" @click="handleUpload">
                Browse
              </el-button> -->
            </div>
          </div>
        </el-dialog>
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { addTMailMaster, delTMailMaster, getTMailMaster, listTMailMaster, updateTMailMaster } from '@/api/mail/mail'
import { propTypeList } from '@/api/gm/srv-gm'

import store from '../../../store'
import XLSX from 'xlsx'

export default {
  name: 'TMailMaster',
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      open2: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      tMailMasterList: [],
      mailTypeOptions: [], mailActiveOptions: [], mailOperateSourceOptions: [], languageTypeOptions: [],
      // 关系表类型
      propTypeOptions: [],

      // Excel导入
      configAllHeader: {},
      configAllBody: {},
      configFieldMasterList: [],
      configSheetNames: [], // 导入Excel的所有Sheet名
      activeName: '',
      importFileName: '', // 文件名
      // 用户导入参数
      uploadFrame: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false
      },
      excelData: {
        planetId: null,
        sheet: null,
        header: null,
        results: null
      },

      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        planetId: undefined,
        mailTitle: undefined,
        mailDetail: undefined,
        mailRewardJson: undefined,
        mailUserTag: undefined,
        mailDest: undefined,
        mailType: undefined,
        mailActive: undefined,
        mailOperateSource: undefined

      },
      // 表单参数
      form: {
        props: [{
          propName: '',
          PropType: undefined,
          DeltaCount: undefined,
          hadPropID: false
        }],
        contexts: [{
          title: '',
          context: undefined,
          lang: undefined
        }],
        beginTime: undefined,
        endTime: undefined,
        dateRange: undefined
      },
      // 导入表单参数
      formImport: {
        uid: ''
      },
      dateRange: [],
      pickerOptions2: {
        shortcuts: [{
          text: '往后一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            end.setTime(end.getTime() + 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '往后一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            end.setTime(end.getTime() + 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '往后三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            end.setTime(end.getTime() + 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      value6: '',
      value7: '',
      // 表单校验
      rules: { planetId: [{ required: true, message: 'planet不能为空', trigger: 'blur' }],
        mailTitle: [{ required: true, message: '标题不能为空', trigger: 'blur' }],
        mailDetail: [{ required: true, message: '内容不能为空', trigger: 'blur' }],
        // mailUserTag: [{ required: true, message: '标签不能为空', trigger: 'blur' }],
        // mailDest: [{ required: true, message: '目标UID数组不能为空', trigger: 'blur' }],
        mailType: [{ required: true, message: '类型不能为空（1-奖励2-公告 3-问卷调查）', trigger: 'blur' }],
        mailActive: [{ required: true, message: '操作：1-只读 2-可领奖 3-问卷调查不能为空', trigger: 'blur' }],
        mailOperateSource: [{ required: true, message: '创建来源：1-表单 2-批量导入不能为空', trigger: 'blur' }],
        dateRange: [
          {
            type: 'array',
            required: true,
            message: '请选择日期区间',
            fields: {
              0: { type: 'string', required: true, message: '请选择开始日期' },
              1: { type: 'string', required: true, message: '请选择结束日期' }
            }
          }
        ]
      }
    }
  },
  created() {
    this.getList()
    this.getDicts('mail_type').then(response => {
      this.mailTypeOptions = response.data
    })
    this.getDicts('mail_active').then(response => {
      this.mailActiveOptions = response.data
    })
    this.getDicts('mail_operate_source').then(response => {
      this.mailOperateSourceOptions = response.data
    })
    // this.getDicts('prop_type').then(response => {
    //   this.propTypeOptions = response.data
    //   console.log('prop_type', response.data)
    // })
    this.getDicts('language_type').then(response => {
      this.languageTypeOptions = response.data
      console.log(response.data)
    })
    this.getPropTreeselect()
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      this.queryParams.planetId = store.getters.project[2]
      listTMailMaster(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.tMailMasterList = response.data.list
        for (let index = 0; index < response.data.list.length; index++) {
          var element = response.data.list[index]
          if (element.mailDetai !== undefined) {
            var paraJsonStr = this.jsonView(element.mailDetail)
            this.tMailMasterList[index].mailDetail = JSON.stringify(paraJsonStr, null, 2)
          }
        }
        console.log(response)
        this.total = response.data.count
        this.loading = false
      }
      )
    },
    /** 查询Prop */
    getPropTreeselect() {
      this.loading = true
      propTypeList().then(
        response => {
          var propInfo = response.data
          for (let index = 0; index < propInfo.length; index++) {
            const element = propInfo[index]
            var prop = {}
            prop.label = element.propName
            prop.value = element.propType
            prop.hadPropID = element.propHadPropId === 1
            this.propTypeOptions.push(prop)
          }

          console.log('prop response', this.propTypeOptions)

          this.loading = false
        }
      )
    },
    jsonView(str) {
      if (str == null || str.length === 0) {
        return ''
      } else {
        return JSON.parse(str)
      }
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 取消按钮 导入对话框
    cancel2() {
      this.open2 = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.dateRange = []
      this.form = {

        id: undefined,
        planetId: undefined,
        mailTitle: undefined,
        mailDetail: undefined,
        mailRewardJson: undefined,
        mailUserTag: undefined,
        mailDest: undefined,
        mailType: undefined,
        mailCreatedAt: undefined,
        mailEndAt: undefined,
        mailActive: undefined,
        mailActiveParam: undefined,
        mailOperateSource: undefined,
        mailRemark: undefined,
        props: [],
        contexts: []
      }
      this.resetForm('form')
    },
    getImgList: function() {
      this.form[this.fileIndex] = this.$refs['fileChoose'].resultList[0].fullUrl
    },
    selectItem: function(value, po) {
      // console.log('selectItem', po)
      for (let index = 0; index < this.propTypeOptions.length; index++) {
        const element = this.propTypeOptions[index]
        if (element.value === value) {
          console.log('selectItem', element.value, value, element.hadPropID)
          po.showPropID = element.hadPropID
          break
        }
      }
      // po.propId = value
    },
    removeProp(item) {
      var index = this.form.props.indexOf(item)
      if (index !== -1) {
        this.form.props.splice(index, 1)
      }
      console.log(this.form.props)
    },
    addProp() {
      this.form.props.push({
        value: '',
        key: Date.now(),
        showPropID: true
      })
    },
    removeContext(item) {
      var index = this.form.contexts.indexOf(item)
      if (index !== -1) {
        this.form.contexts.splice(index, 1)
      }
    },
    addContext() {
      this.form.contexts.push({
        title: '',
        context: undefined,
        lang: undefined,
        key: Date.now()
      })
    },
    selectLangItem: function(value, po) {
      console.log(value)
      po.propId = value
    },
    fileClose: function() {
      this.fileOpen = false
    },
    mailTypeFormat(row) {
      return this.selectDictLabel(this.mailTypeOptions, row.mailType)
    },
    mailActiveFormat(row) {
      return this.selectDictLabel(this.mailActiveOptions, row.mailActive)
    },
    mailOperateSourceFormat(row) {
      return this.selectDictLabel(this.mailOperateSourceOptions, row.mailOperateSource)
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加邮件'
      this.isEdit = false
      this.form.props = []
      this.form.props.push({
        value: '',
        key: Date.now()
      })
      this.form.contexts = []
      this.form.contexts.push({
        title: '',
        key: Date.now()
      })
      console.log(this.form.props)
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      console.log(row)
      const id =
              row.id || this.ids
      getTMailMaster(id).then(response => {
        this.reset()
        this.open = true
        this.title = '修改邮件'
        this.isEdit = true
        // this.form = response.data
        var data = response.data
        console.log('handleUpdate form', this.form)
        this.form.id = id
        this.form.mailActive = data.mailActive + ''
        this.form.mailActiveParam = data.mailActiveParam
        this.form.mailCreatedAt = data.mailCreatedAt
        this.form.mailDest = data.mailDest
        this.form.mailEndAt = data.mailEndAt
        this.form.mailOperateSource = data.mailOperateSource
        this.form.mailRemark = data.mailRemark
        this.form.mailTitle = data.mailTitle
        this.form.mailUserTag = data.mailUserTag
        this.form.planetId = data.planetId
        this.form.mailOperateSource = data.mailOperateSource + ''
        this.form.mailType = data.mailType + ''

        this.dateRange = [data.mailCreatedAt, data.mailEndAt]

        // 内容
        this.form.contexts = []
        var ctxs = JSON.parse(data.mailDetail)
        this.form.contexts = ctxs

        // 奖励
        this.form.props = []
        var reward = JSON.parse(data.mailRewardJson)
        this.form.props = reward
        console.log(this.form)
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        console.log('submit form', this.form)
        if (valid) {
          console.log('this.dateRange', this.dateRange)
          var dataRet = this.addDateRange(this.form, this.dateRange)
          this.form.mailCreatedAt = dataRet.beginTime
          this.form.mailEndAt = dataRet.endTime

          if (this.form.contexts.length === 0) {
            this.msgError('请配置邮件内容')
            return
          }

          if (this.form.beginTime === '' || this.form.beginTime === undefined ||
              this.form.endTime === '' || this.form.endTime === undefined) {
            this.msgError('请选择正确的时间区间')
            return
          }

          if (this.form.beginTime >= this.form.endTime) {
            this.msgError('请选择正确的时间区间')
            return
          }

          if (this.form.mailType === 3 && this.form.mailActiveParam === undefined) {
            this.msgError('问卷调查必须填写对应的附加链接')
            return
          }

          if (this.form.props.length === 0 && this.form.mailType === 1) {
            this.msgError('请配置奖励')
            return
          }
          if (this.form.props.some(item => !item.PropType)) {
            this.msgError('请选择物品')
            return
          }

          if ((this.form.mailDest === '' && this.form.mailRemark === '') ||
          this.form.mailDest === undefined && this.form.mailRemark === undefined) {
            this.msgError('没有配置发送对象，必须要填写备注表明已知发送给所有玩家')
            return
          }

          for (let ii = 0; ii < this.form.props.length; ii++) {
            var propIdShow = this.form.props[ii].showPropID
            if (propIdShow && this.form.props[ii].propId === undefined) {
              this.msgError('选择的物品需要配置PropID')
              return
            }
          }

          this.form.planetId = store.getters.project[2]
          this.form.mailType = parseInt(this.form.mailType)
          this.form.mailActive = parseInt(this.form.mailActive)
          this.form.mailOperateSource = 1
          this.form.mailTitle = this.form.contexts[0].title
          this.form.mailDetail = JSON.stringify(this.form.contexts)
          var saveObj = []
          for (let index = 0; index < this.form.props.length; index++) {
            const element = this.form.props[index]
            var row = {}
            row.propId = String(element.propId)
            row.PropType = element.PropType
            row.DeltaCount = element.DeltaCount
            saveObj.push(row)
          }
          this.form.mailRewardJson = JSON.stringify(saveObj)

          console.log('send form', this.form)

          if (this.form.id !== undefined) {
            updateTMailMaster(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          } else {
            addTMailMaster(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          }
        }
      })
    },
    handleClickUseID() {
      console.log('导入值', this.configFieldMasterList)
      var realStr = ''
      for (let index = 0; index < this.configFieldMasterList.length; index++) {
        if (index <= 1) {
          continue
        }
        const element = this.configFieldMasterList[index]
        realStr = realStr + element + '|'
      }
      this.form.mailDest = realStr
      this.uploadFrame.open = false
      this.open2 = false
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var Ids = (row.id && [row.id]) || this.ids

      this.$confirm('是否确认撤销MailID为"' + Ids + '"的邮件?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delTMailMaster({ 'ids': Ids })
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function() {
      })
    },
    /** 导入按钮操作 */
    handleImport() {
      this.uploadFrame.title = '批量导入玩家ID'
      this.uploadFrame.open = true
    },
    handleDrop(e) {
      e.stopPropagation()
      e.preventDefault()
      if (this.loading) return
      const files = e.dataTransfer.files
      if (files.length !== 1) {
        this.$message.error('Only support uploading one file!')
        return
      }
      const rawFile = files[0] // only use files[0]

      if (!this.isExcel(rawFile)) {
        this.$message.error('Only supports upload .xlsx, .xls, .csv suffix files')
        return false
      }
      this.upload(rawFile)
      e.stopPropagation()
      e.preventDefault()
    },
    handleDragover(e) {
      e.stopPropagation()
      e.preventDefault()
      e.dataTransfer.dropEffect = 'copy'
    },
    handleUpload() {
      this.$refs['excel-upload-input'].click()
    },
    handleClick(e) {
      const files = e.target.files
      const rawFile = files[0] // only use files[0]
      console.log('rawFile', rawFile.name)
      this.importFileName = rawFile.name
      if (!rawFile) return
      this.upload(rawFile)
    },
    handleTab(tab) {
      console.log('tab.name', tab.name)
      this.changeTab(tab.name)
    },
    upload(rawFile) {
      this.$refs['excel-upload-input'].value = null // fix can't select the same excel

      if (!this.beforeUpload) {
        this.readerData(rawFile)
        return
      }

      const before = this.beforeUpload(rawFile)
      if (before) {
        this.readerData(rawFile)
      }
    },
    changeTab(sheetName) {
      // 首先读取第一个Sheet
      this.loading = true
      this.selectSheetName = sheetName
      this.activeName = sheetName
      const results = this.configAllBody[sheetName]

      // console.log('changeTab header', header)
      // console.log('changeTab results', results)

      if (results === 'undefined' || results.length === 0) {
        return
      }

      var formatStr = ''
      var count = 1
      for (let index = 0; index < results.length; index++) {
        if (index <= 1) {
          continue
        }
        const element = results[index]
        formatStr = formatStr + element + '        '
        if (count > 0 && count % 10 === 0) {
          // console.log(count + '换行' + index)
          formatStr += '\n'
        }
        count++
      }

      this.formImport.uid = formatStr
      this.configFieldMasterList = results
      // this.title = '导入玩家UID' + this.importFileName
      this.title = '导入玩家UID'
      this.open2 = true
      this.isEdit = false
      this.loading = false
    },
    readerData(rawFile) {
      this.loading = true
      return new Promise((resolve) => {
        const reader = new FileReader()
        reader.onload = e => {
          const data = e.target.result
          const workbook = XLSX.read(data, { type: 'array' })
          this.configSheetNames = workbook.SheetNames

          for (let index = 0; index < this.configSheetNames.length; index++) {
            const sheetName = workbook.SheetNames[index]
            const aHeadCenum = sheetName.startsWith('c.')

            const worksheet = workbook.Sheets[sheetName]
            // const header = this.getHeaderRow(worksheet, { header: 1, defval: '' })
            const header = this.getHeaderRow(worksheet, { header: 1 })
            const result = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
            if (aHeadCenum) {
              // 转换Const为列
              var newHeader = []
              var newRow = []
              for (let index = 1; index < result.length; index++) { // 过滤const的标题
                const element = result[index]
                newRow[index - 1] = element[0]
                newHeader[index - 1] = element[2]
              }
              var newResult = []
              newResult.push(newHeader)
              newResult.push(newRow)

              this.configAllHeader[sheetName] = newHeader
              this.configAllBody[sheetName] = newResult
            } else {
              this.configAllHeader[sheetName] = header
              this.configAllBody[sheetName] = result
            }
            // console.log('this.configAllBody', this.configAllBody)
          }

          // 默认显示第一个sheet
          const firstSheetName = workbook.SheetNames[0]
          this.changeTab(firstSheetName)

          this.loading = false
          resolve()
        }
        reader.readAsArrayBuffer(rawFile)
      })
    },
    getHeaderRow(sheet) {
      const headers = []
      const range = XLSX.utils.decode_range(sheet['!ref'])
      let C
      const R = range.s.r
      /* start in the first row */
      for (C = range.s.r; C <= range.e.c; ++C) { /* walk every column in the range */
        const cell = sheet[XLSX.utils.encode_cell({ c: C, r: R })]
        /* find the cell in the first row */
        // let hdr = 'UNKNOWN ' + C // <-- replace with your desired default
        let hdr = '' // <-- empty default
        if (cell && cell.t) hdr = XLSX.utils.format_cell(cell)
        headers.push(hdr)
      }
      return headers
    },
    isExcel(file) {
      return /\.(xlsx|xls|csv)$/.test(file.name)
    },
    // methods中写方法
    cellStyle(data) {
      var fieldRuleId = data.row.fieldRuleId
      if (fieldRuleId !== '') {
        var checkIdArray = JSON.parse(fieldRuleId)
        if (checkIdArray.length === 0) {
          return 'color: gray'
        }
        return 'color: green'
      }

      return 'color: gray'
    }
  }
}
</script>

<style>
  .text {
    font-size: 14px;
  }

  .item {
    margin-bottom: 18px;
  }

  .clearfix:before,
  .clearfix:after {
    display: table;
    content: "";
  }
  .clearfix:after {
    clear: both
  }

  .row-con {
    display: flex;
    flex-flow: row wrap;
  }
  .row-con .card {
    height: 100%;
  }

  .box-card-sub {
    width: 900px;
  }
</style>
