
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card-head">
        <div slot="header" size="small" class="clearfix">
          <span>链接生成</span>
        </div>
        <el-form ref="addForm" :model="dynamicValidateForm" :inline="true" :rules="rulesAdd">
          <el-row>
            <el-form-item label="有效时间">
              <el-date-picker
                v-model="dateRange"
                size="small"
                type="datetimerange"
                :picker-options="pickerOptions"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                align="right"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
          </el-row>
          <el-row :gutter="12">
            <el-form-item
              v-for="(po, index) in dynamicValidateForm.props"
              :key="po.key"
              align="right"
              :label="'道具' + (index + 1)"
              :prop="'props.' + index + '.DeltaCount'"
              :rules="{
                required: true, message: '请填写正确物品信息', trigger: 'blur'
              }"
            >
              <el-col :span="8">
                <!-- <el-input v-model="po.propName" /> -->
                <el-select
                  v-model="po.PropType"
                  placeholder="选择道具"
                  clearable
                  @change="selectItem($event, po)"
                >
                  <el-option
                    v-for="dict in propTypeOptions"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select></el-col>
              <el-col :span="4">
                <el-input v-model="po.propId" :disabled="true" />
              </el-col>
              <el-col :span="6">
                <el-input-number v-model="po.DeltaCount" :min="1" label="数量" />
              </el-col>
              <el-col :span="4">
                <el-button type="danger" size="small" icon="el-icon-delete" @click.prevent="removeProp(po)">删除</el-button>
              </el-col>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item>
              <el-button type="primary" icon="el-icon-key" @click="submitForm('dynamicValidateForm')">生成链接</el-button>
              <el-button type="warning" icon="el-icon-plus" @click="addProp">添加道具</el-button>
            </el-form-item>
          </el-row>
        </el-form>
      </el-card>

      <el-card class="box-card">
        <div slot="header" size="small" class="clearfix">
          <span>操作记录</span>
        </div>
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
          <el-row>
            <el-form-item>
              <el-date-picker
                v-model="dateRange"
                size="small"
                type="datetimerange"
                :picker-options="pickerOptions"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                align="right"
                value-format="yyyy-MM-dd HH:mm:ss"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查看历史</el-button>
              <!-- <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button> -->
            </el-form-item>
          </el-row></el-form>

        <el-table v-loading="loading" :data="tFansPageList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" /><el-table-column
            label="开始时间"
            align="center"
            prop="startTimeShow"
            width="160"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.startTimeShow) }}</span>
            </template>
          </el-table-column><el-table-column
            label="结束时间"
            align="center"
            prop="endTimeShow"
            width="160"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.endTimeShow) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="长链接"
            align="center"
            prop="urlLong"
            width="400"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <div @click="handelCopy(scope.row.urlLong)">{{ scope.row.urlLong }}</div>
            </template>
          </el-table-column>
          <el-table-column
            label="短链接"
            align="center"
            prop="urlShort"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <div @click="handelCopy(scope.row.urlShort)">{{ scope.row.urlShort }}</div>
            </template>
          </el-table-column>
          <el-table-column
            label="奖励Json"
            align="center"
            prop="rewardJson"
            width="120"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="备注"
            align="center"
            prop="remark"
            :show-overflow-tooltip="true"
          />
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { addTFansPage, delTFansPage, getTFansPage, listTFansPage } from '@/api/business/fans'
import { listTPlantParams } from '@/api/plant/plant_params'
import store from '../../../store'

export default {
  name: 'TFansPage',
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      tFansPageList: [],
      propTypeOptions: [],
      // 关系表类型

      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        planetId: undefined,
        startTimeShow: undefined,
        endTimeShow: undefined
      },
      dynamicValidateForm: {
        props: [{
          propName: '',
          PropType: undefined,
          DeltaCount: undefined
        }],
        startTimeShow: undefined,
        endTimeShow: undefined
      },
      // 日期范围
      dateRange: [],
      tPlantParamsList: [],
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      // 表单参数
      form: {
      },
      // 表单校验
      rulesAdd: {
        dateRange: [{ required: true, type: 'date', message: '时间不能为空', trigger: 'blur', pattern: /.+/ }]
      }
    }
  },
  created() {
    this.getList()
    this.getDicts('prop_type').then(response => {
      this.propTypeOptions = response.data
      console.log(response.data)
    })
    var queryParam = {}
    queryParam.paramKey = 'url_fan_page'
    queryParam.plantId = store.getters.project[2]
    listTPlantParams(this.addDateRange(queryParam, this.dateRange)).then(response => {
      this.tPlantParamsList = response.data.list
      console.log(this.tPlantParamsList[0].paramValue)
    }
    )
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      this.queryParams.planetId = store.getters.project[2]
      listTFansPage(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.tFansPageList = response.data.list
        this.total = response.data.count
        this.loading = false
      }
      )
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {

        id: undefined,
        startTimeShow: undefined,
        startTimeValue: undefined,
        endTimeShow: undefined,
        endTimeValue: undefined,
        urlLong: undefined,
        urlShort: undefined,
        cdkey: undefined,
        rewardJson: undefined,
        rpcJson: undefined,
        remark: undefined
      }
      this.resetForm('form')
    },
    reset2() {
      this.dynamicValidateForm = {

        props: [{
          propName: '',
          PropType: undefined,
          DeltaCount: undefined
        }],
        startTimeShow: undefined,
        endTimeShow: undefined
      }
      this.resetForm('form')
    },
    getImgList: function() {
      this.form[this.fileIndex] = this.$refs['fileChoose'].resultList[0].fullUrl
    },
    fileClose: function() {
      this.fileOpen = false
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    removeProp(item) {
      var index = this.dynamicValidateForm.props.indexOf(item)
      if (index !== -1) {
        this.dynamicValidateForm.props.splice(index, 1)
      }
    },
    addProp() {
      this.dynamicValidateForm.props.push({
        value: '',
        key: Date.now()
      })
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加TFansPage'
      this.isEdit = false
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.form.planetId = store.getters.project[2]
      const id =
                row.id || this.ids
      getTFansPage(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改TFansPage'
        this.isEdit = true
      })
    },
    selectItem: function(value, po) {
      console.log(value)
      po.propId = value
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['addForm'].validate(valid => {
        if (valid) {
          var bean = this.addDateRange(this.dynamicValidateForm, this.dateRange)

          bean.startTimeValue = bean.beginTime
          bean.endTimeValue = bean.endTime
          bean.planetId = store.getters.project[2]
          bean.urlLong = this.tPlantParamsList[0].paramValue

          console.log(bean.startTimeValue)
          if (bean.startTimeValue === undefined || bean.endTimeValue === undefined) {
            this.msgError('请选择有效时间')
            return
          }

          bean.rewardJson = JSON.stringify(bean.props)
          console.log(bean)
          addTFansPage(bean).then(response => {
            if (response.code === 200) {
              this.msgSuccess(response.msg)
              this.open = false
              this.getList()
              this.resetForm('dynamicValidateForm')
            } else {
              this.msgError(response.msg)
            }
          })
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var Ids = (row.id && [row.id]) || this.ids

      this.$confirm('是否确认删除编号为"' + Ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delTFansPage({ 'ids': Ids })
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function() {
      })
    },
    /** 复制 **/
    handelCopy(url) {
      /**
      navigator.clipboard.writeText(url).then(() => {
        this.$Message.success('复制成功')
      })
      **/
      const oInput = document.createElement('input')
      oInput.value = url
      document.body.appendChild(oInput)
      oInput.select() // 选择对象
      document.execCommand('Copy') // 执行浏览器复制命令
      this.$message({
        showClose: true,
        message: '复制成功',
        type: 'success'
      })
      oInput.remove()
    }
  }
}
</script>
<style lang="scss">
    .el-tooltip__popper{max-width:500px}
</style>
