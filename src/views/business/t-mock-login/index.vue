
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
          <!-- <el-form-item label="用户ID" prop="uid"><el-input
            v-model="queryParams.uid"
            placeholder="请输入用户ID"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item> -->
          <el-form-item label="YouUID" prop="yourUid"><el-input
            v-model="queryParams.yourUid"
            placeholder="请输入your_uid"
            clearable
            size="mini"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="MockUID" prop="replacedUid"><el-input
            v-model="queryParams.replacedUid"
            placeholder="请输入replaced_uid"
            clearable
            size="mini"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="开始时间" prop="timeBegin">
            <!-- <el-input
            v-model="queryParams.timeBegin"
            placeholder="请输入开始时间"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          /> -->
            <el-date-picker
              v-model="dateRange"
              size="mini"
              type="datetimerange"
              :picker-options="pickerOptions"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              align="right"
              value-format="yyyy-MM-dd HH:mm:ss"
            />

          </el-form-item>
          <!-- <el-form-item label="逻辑删除标记 1：删除" prop="deleteFlg"><el-input
            v-model="queryParams.deleteFlg"
            placeholder="请输入逻辑删除标记 1：删除"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item> -->
          <el-form-item>
            <el-checkbox v-model="queryParams.deleteFlg" :true-label="0" :false-label="undefined" :checked="false">仅未清除</el-checkbox>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              v-permisaction="['business:tMockLogin:add']"
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
            >新增
            </el-button>
          </el-col>
        </el-row>

        <el-table v-loading="loading" :data="tMockLoginList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" /><el-table-column
            label="用户ID"
            align="center"
            prop="uid"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="your_uid"
            align="center"
            prop="yourUid"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="replaced_uid"
            align="center"
            prop="replacedUid"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="开始时间"
            align="center"
            prop="timeBegin"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.timeBegin) }}</span>
            </template>
          </el-table-column><el-table-column
            label="备注"
            align="center"
            prop="remark"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button
                v-if="scope.row.deleteFlg == 0"
                slot="reference"
                v-permisaction="['business:tMockLogin:remove']"
                size="small"
                type="success"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
              >去清除
              </el-button>
              <el-button
                v-if="scope.row.deleteFlg == 1"
                slot="reference"
                v-permisaction="['business:tMockLogin:remove']"
                size="small"
                type="info"
                icon="el-icon-delete"
                enable="true"
              >已清除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="600px">
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">

            <el-form-item label="planet" prop="planetId">
              <el-input
                v-model="form.planetId"
                placeholder="planet"
                readonly="true"
              />
            </el-form-item>
            <el-form-item label="操作者" prop="uid">
              <el-input
                v-model="form.uid"
                placeholder="操作者"
                readonly="true"
              />
            </el-form-item>
            <el-form-item label="your" prop="yourUid">
              <el-input
                v-model="form.yourUid"
                placeholder="your_uid"
              />
            </el-form-item>
            <el-form-item label="replaced" prop="replacedUid">
              <el-input
                v-model="form.replacedUid"
                placeholder="replaced_uid"
              />
            </el-form-item>
            <el-form-item label="开始时间" prop="timeBegin">
              <el-date-picker
                v-model="form.timeBegin"
                type="datetime"
                placeholder="选择日期"
              />
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="form.remark"
                placeholder="备注"
              />
            </el-form-item>
            <!-- <el-form-item label="逻辑删除标记 1：删除" prop="deleteFlg">
              <el-input
                v-model="form.deleteFlg"
                placeholder="逻辑删除标记 1：删除"
              />
            </el-form-item> -->
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { addTMockLogin, delTMockLogin, getTMockLogin, listTMockLogin, updateTMockLogin } from '@/api/business/mockLogin'
import store from '../../../store'

export default {
  name: 'TMockLogin',
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      tMockLoginList: [],
      // 日期范围
      pickerOptions: {},
      dateRange: [],

      // 关系表类型

      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        uid: undefined,
        yourUid: undefined,
        replacedUid: undefined,
        timeBegin: undefined,
        deleteFlg: undefined,
        planetId: undefined
      },
      // 表单参数
      form: {
      },
      // 表单校验
      rules: { uid: [{ required: true, message: '用户ID不能为空', trigger: 'blur' }],
        yourUid: [{ required: true, message: 'your_uid不能为空', trigger: 'blur' }],
        replacedUid: [{ required: true, message: 'replaced_uid不能为空', trigger: 'blur' }],
        timeBegin: [{ required: true, message: '开始时间不能为空', trigger: 'blur' }],
        deleteFlg: [{ required: true, message: '逻辑删除标记 1：删除不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      // console.log(this.queryParams)

      if (this.queryParams.deleteFlg === false) {
        this.queryParams.deleteFlg = undefined
      }
      this.queryParams.planetId = store.getters.project[2]

      listTMockLogin(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.tMockLoginList = response.data.list
        this.total = response.data.count
        this.loading = false
      }
      )
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {

        id: undefined,
        planetId: undefined,
        uid: undefined,
        yourUid: undefined,
        replacedUid: undefined,
        timeBegin: undefined,
        remark: undefined,
        deleteFlg: undefined
      }
      this.resetForm('form')
    },
    getImgList: function() {
      this.form[this.fileIndex] = this.$refs['fileChoose'].resultList[0].fullUrl
    },
    fileClose: function() {
      this.fileOpen = false
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.form.planetId = store.getters.project[2]

      this.form.uid = store.getters.name

      this.title = '添加TMockLogin'
      this.isEdit = false
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id =
              row.id || this.ids
      getTMockLogin(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改TMockLogin'
        this.isEdit = true
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id !== undefined) {
            updateTMockLogin(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          } else {
            addTMockLogin(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var Ids = (row.id && [row.id]) || this.ids
      console.log(Ids)

      this.$confirm('确认清除?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delTMockLogin({ 'ids': Ids })
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function() {
      })
    }
  }
}
</script>
