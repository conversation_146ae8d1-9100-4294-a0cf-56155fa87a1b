
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="平台" prop="platform"><el-select
            v-model="queryParams.platform"
            placeholder="TFbLoginFailedPlatform"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in platformOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          </el-form-item>
          <el-form-item label="时间" prop="timeStampValue">
            <div class="block">
              <el-date-picker
                v-model="queryParams.timeStampValue"
                type="datetime"
                size="small"
                placeholder="选择日期"
                @keyup.enter.native="handleQuery"
              />
            </div>
          </el-form-item>
          <el-form-item label="登录类型" prop="accType"><el-select
            v-model="queryParams.accType"
            placeholder="TFbLoginFailedAccType"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in accTypeOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          </el-form-item>
          <el-form-item label="版本号" prop="appVersion"><el-input
            v-model="queryParams.appVersion"
            placeholder="请输入AppVersion"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="语言" prop="appLanguage"><el-select
            v-model="queryParams.appLanguage"
            placeholder="TFbLoginFailedAppLanguage"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in appLanguageOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          </el-form-item>
          <el-form-item label="国家" prop="country"><el-input
            v-model="queryParams.country"
            placeholder="请输入Country"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="Reason" prop="reason"><el-input
            v-model="queryParams.reason"
            placeholder="请输入Reason"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
      <el-card class="box-card">
        <el-table
          v-loading="loading"
          :data="tFbLoginFailedGroupList"
          stripe
          style="width: 100%"
          title="错误汇总"
        >
          <el-table-column
            prop="Reason"
            label="错误"
            width="600"
          />
          <el-table-column
            prop="Count"
            label="数量"
          />
        </el-table>
      </el-card>
      <el-card class="box-card">
        <el-table v-loading="loading" :data="tFbLoginFailedList" @selection-change="handleSelectionChange">
          <!-- <el-table-column type="selection" width="55" align="center" /> -->
          <el-table-column
            label="平台"
            align="left"
            prop="platform"
            :formatter="platformFormat"
            width="100"
          >
            <template slot-scope="scope">
              {{ platformFormat(scope.row) }}
            </template>
          </el-table-column><el-table-column
            label="时间"
            align="left"
            prop="timeStampValue"
            width="160"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.timeStampValue) }}</span>
            </template>
          </el-table-column><el-table-column
            label="登录类型"
            align="left"
            prop="accType"
            :formatter="accTypeFormat"
            width="120"
          >
            <template slot-scope="scope">
              {{ accTypeFormat(scope.row) }}
            </template>
          </el-table-column>
          <!-- <el-table-column
            label="Channel"
            align="center"
            prop="channel"
            :show-overflow-tooltip="true"
          /> -->
          <el-table-column
            label="版本号"
            align="left"
            prop="appVersion"
            width="100"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="语言"
            align="left"
            prop="appLanguage"
            :formatter="appLanguageFormat"
            width="100"
          >
            <template slot-scope="scope">
              {{ appLanguageFormat(scope.row) }}
            </template>
          </el-table-column><el-table-column
            label="国家"
            align="left"
            prop="country"
            width="100"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="DeviceID"
            align="left"
            prop="deviceID"
            width="220"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="Reason"
            align="left"
            prop="reason"
            :show-overflow-tooltip="true"
          />
          <!-- <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要修改吗?"
                confirm-button-text="修改"
                @onConfirm="handleUpdate(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['business:tFbLoginFailed:edit']"
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                >修改
                </el-button>
              </el-popconfirm>
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要删除吗?"
                confirm-button-text="删除"
                @onConfirm="handleDelete(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['business:tFbLoginFailed:remove']"
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                >删除
                </el-button>
              </el-popconfirm>
            </template>
          </el-table-column> -->
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px">
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">

            <el-form-item label="Platform" prop="platform">
              <el-select
                v-model="form.platform"
                placeholder="请选择"
              >
                <el-option
                  v-for="dict in platformOptions"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="NowDate" prop="nowDate">
              <el-date-picker
                v-model="form.nowDate"
                type="datetime"
                placeholder="选择日期"
              />
            </el-form-item>
            <el-form-item label="TimeStampValue" prop="timeStampValue">
              <el-date-picker
                v-model="form.timeStampValue"
                type="datetime"
                placeholder="选择日期"
              />
            </el-form-item>
            <el-form-item label="AccType" prop="accType">
              <el-select
                v-model="form.accType"
                placeholder="请选择"
              >
                <el-option
                  v-for="dict in accTypeOptions"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="Channel" prop="channel">
              <el-input
                v-model="form.channel"
                placeholder="Channel"
              />
            </el-form-item>
            <el-form-item label="AppVersion" prop="appVersion">
              <el-input
                v-model="form.appVersion"
                placeholder="AppVersion"
              />
            </el-form-item>
            <el-form-item label="AppLanguage" prop="appLanguage">
              <el-input
                v-model="form.appLanguage"
                placeholder="AppLanguage"
              />
            </el-form-item>
            <el-form-item label="Country" prop="country">
              <el-input
                v-model="form.country"
                placeholder="Country"
              />
            </el-form-item>
            <el-form-item label="DeviceID" prop="deviceID">
              <el-input
                v-model="form.deviceID"
                placeholder="DeviceID"
              />
            </el-form-item>
            <el-form-item label="Reason" prop="reason">
              <el-input
                v-model="form.reason"
                placeholder="Reason"
              />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { addTFbLoginFailed, delTFbLoginFailed, getTFbLoginFailed, listTFbLoginFailed, updateTFbLoginFailed, listGroupTFbLoginFailed } from '@/api/business/fbLoginFailed'

export default {
  name: 'TFbLoginFailed',
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      total2: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      tFbLoginFailedList: [],
      tFbLoginFailedGroupList: [],
      platformOptions: [], accTypeOptions: [], appLanguageOptions: [],
      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        pageGroupSize: 5,
        platform: undefined,
        timeStampValue: undefined,
        nowDate: undefined,
        accType: undefined,
        appVersion: undefined,
        appLanguage: undefined,
        country: undefined,
        reason: undefined

      },
      // 表单参数
      form: {
      },
      // 表单校验
      rules: { platform: [{ required: true, message: 'Platform不能为空', trigger: 'blur' }],
        timeStampValue: [{ required: true, message: 'TimeStampValue不能为空', trigger: 'blur' }],
        accType: [{ required: true, message: 'AccType不能为空', trigger: 'blur' }],
        appVersion: [{ required: true, message: 'AppVersion不能为空', trigger: 'blur' }],
        appLanguage: [{ required: true, message: 'AppLanguage不能为空', trigger: 'blur' }],
        country: [{ required: true, message: 'Country不能为空', trigger: 'blur' }],
        reason: [{ required: true, message: 'Reason不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
    this.getGroup()
    this.getDicts('platform').then(response => {
      this.platformOptions = response.data
    })
    this.getDicts('acc_type').then(response => {
      this.accTypeOptions = response.data
    })
    this.getDicts('language_type').then(response => {
      this.appLanguageOptions = response.data
    })
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      listTFbLoginFailed(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.tFbLoginFailedList = response.data.list
        this.total = response.data.count
        this.loading = false
      }
      )
    },
    getGroup() {
      this.loading = true

      console.log(this.queryParams)

      // 获取汇总信息
      listGroupTFbLoginFailed(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.tFbLoginFailedGroupList = response.data.list
        this.total2 = response.data.count
        console.log('tFbLoginFailedGroupList', this.tFbLoginFailedGroupList)
        // this.loading = false
      }
      )
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {

        id: undefined,
        platform: undefined,
        nowDate: undefined,
        timeStampValue: undefined,
        accType: undefined,
        channel: undefined,
        appVersion: undefined,
        appLanguage: undefined,
        country: undefined,
        deviceID: undefined,
        reason: undefined
      }
      this.resetForm('form')
    },
    getImgList: function() {
      this.form[this.fileIndex] = this.$refs['fileChoose'].resultList[0].fullUrl
    },
    fileClose: function() {
      this.fileOpen = false
    },
    platformFormat(row) {
      return this.selectDictLabel(this.platformOptions, row.platform)
    },
    accTypeFormat(row) {
      return this.selectDictLabel(this.accTypeOptions, row.accType)
    },
    appLanguageFormat(row) {
      return this.selectDictLabel(this.appLanguageOptions, row.appLanguage)
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
      this.getGroup()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加TFbLoginFailed'
      this.isEdit = false
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      console.log('handleUpdate', row)
      const id = 1
      getTFbLoginFailed(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改TFbLoginFailed'
        this.isEdit = true
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id !== undefined) {
            updateTFbLoginFailed(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          } else {
            addTFbLoginFailed(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var Ids = (row.id && [row.id]) || this.ids

      this.$confirm('是否确认删除编号为"' + Ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delTFbLoginFailed({ 'ids': Ids })
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function() {
      })
    }
  }
}
</script>
