
<template>
  <BasicLayout>
    <template #wrapper>
      <div class="dashboard-editor-container">
        <el-row :gutter="12">
          <el-col :sm="24" :xs="24" :md="6" :xl="6" :lg="6" :style="{ marginBottom: '12px' }">
            <chart-card title="推送成功" :total="totalOk+'次'">
              <el-tooltip slot="action" class="item" effect="dark" content="检索条件范围内结果" placement="top-start">
                <i class="el-icon-warning-outline" />
              </el-tooltip>
              <div>
                <trend flag="top" style="margin-right: 16px;" :rate="rateSucc+''">
                  <span slot="term">成功率</span>
                </trend>
                <trend flag="bottom" :rate="rateFail+''">
                  <span slot="term">失败率</span>
                </trend>
              </div>
              <template slot="footer">总推送次数<span> {{ totalSum }} </span></template>
            </chart-card>
          </el-col>
          <!-- <el-col :sm="24" :xs="24" :md="6" :xl="6" :lg="6" :style="{ marginBottom: '12px' }">
            <chart-card title="日推送成功量" :total="8846">
              <el-tooltip slot="action" class="item" effect="dark" content="推送趋势" placement="top-start">
                <i class="el-icon-warning-outline" />
              </el-tooltip>
              <div>
                <mini-area />
              </div>
              <template slot="footer">当日<span> {{ '1234' }}</span></template>
            </chart-card>
          </el-col> -->
        </el-row>
        <el-card class="box-card">
          <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
            <el-form-item label="用户ID" prop="uid"><el-input
              v-model="queryParams.uid"
              placeholder="请输入用户ID"
              clearable
              size="mini"
              @keyup.enter.native="handleQuery"
            />
            </el-form-item>
            <el-form-item label="版本语言" prop="lang"><el-input
              v-model="queryParams.lang"
              placeholder="请输入版本语言"
              clearable
              size="mini"
              @keyup.enter.native="handleQuery"
            />
            </el-form-item>
            <el-form-item label="PushType" prop="pushType"><el-select
              v-model="queryParams.pushType"
              placeholder="TPushLogPushType"
              clearable
              size="mini"
            >
              <el-option
                v-for="dict in pushTypeOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
            </el-form-item>
            <el-form-item label="PushId" prop="pushId"><el-select
              v-model="queryParams.pushId"
              placeholder="TPushLogPushId"
              clearable
              size="mini"
            >
              <el-option
                v-for="dict in pushIdOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
            </el-form-item>
            <el-form-item label="时间" prop="timeStampValue">
              <el-date-picker
                v-model="dateRange"
                size="mini"
                type="datetimerange"
                :picker-options="pickerOptions"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                align="right"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>

          <el-table v-loading="loading" :data="tPushLogList" @selection-change="handleSelectionChange">
            <!-- <el-table-column type="selection" width="55" align="center" /> -->
            <el-table-column
              label="用户ID"
              align="center"
              width="120"
              prop="uid"
              :show-overflow-tooltip="true"
            /><el-table-column
              label="语言"
              align="center"
              width="100"
              prop="lang"
              :show-overflow-tooltip="true"
            /><el-table-column
              label="PushType"
              align="center"
              prop="pushType"
              :formatter="pushTypeFormat"
              width="140"
            >
              <template slot-scope="scope">
                {{ pushTypeFormat(scope.row) }}
              </template>
            </el-table-column><el-table-column
              label="PushId"
              align="center"
              prop="pushId"
              :formatter="pushIdFormat"
              width="140"
            >
              <template slot-scope="scope">
                {{ pushIdFormat(scope.row) }}
              </template>
            </el-table-column><el-table-column
              label="总数"
              align="center"
              width="120"
              prop="pushTotalNum"
              :show-overflow-tooltip="true"
            /><el-table-column
              label="成功数"
              align="center"
              width="120"
              prop="pushOkNum"
              :show-overflow-tooltip="true"
            /><el-table-column
              label="失败数"
              align="center"
              width="120"
              prop="pushFailNum"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="timeStampValue"
              align="center"
              prop="timeStampValue"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.timeStampValue) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
              <!-- <template slot-scope="scope">
                <el-button
                  slot="reference"
                  v-permisaction="['business:tPushLog:edit']"
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @onClick="handleUpdate(scope.row)"
                >修改
                </el-button>
              </template> -->
              <template slot-scope="scope">
                <el-button
                  v-permisaction="['business:tPushLog:edit']"
                  size="mini"
                  type="text"
                  icon="el-icon-view"
                  @click="handleView(scope.row,scope.index)"
                >详细</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageIndex"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />

          <!-- 添加或修改对话框 -->
          <el-dialog :title="title" :visible.sync="open" width="800px">
            <el-form ref="form" :model="form" :rules="rules" label-width="80px">
              <el-row>
                <el-col :span="8" align="left">
                  <el-form-item label="玩家ID：">{{ form.uid }}</el-form-item>
                </el-col>
                <el-col :span="8" align="left">
                  <el-form-item label="推送：">{{ form.pushType }}</el-form-item>
                </el-col>
                <el-col :span="8" align="left" width="240">
                  <el-form-item label="推送ID：">{{ form.pushId }}</el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8" align="left">
                  <el-form-item label="总数：">{{ form.pushTotalNum }}</el-form-item>
                </el-col>
                <el-col :span="8" align="left">
                  <el-form-item label="成功数：">{{ form.pushOkNum }}</el-form-item>
                </el-col>
                <el-col :span="8" align="left">
                  <el-form-item label="失败数：">{{ form.pushFailNum }}</el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8" align="left">
                  <el-form-item label="语言：">{{ form.lang }}</el-form-item>
                </el-col>
                <el-col :span="8" align="left">
                  <el-form-item label="时间：">{{ form.timeStampValue }}</el-form-item>
                </el-col>
              </el-row>
              <el-col>
                <el-col :span="24" align="left">
                  <el-form-item label="结果：">{{ form.pushErrorMsg }}</el-form-item>
                </el-col>
              </el-col>
            </el-form>
            <div slot="footer" class="dialog-footer">
              <el-button @click="cancel">确 定</el-button>
            </div>
          </el-dialog>
        </el-card>
      </div>
    </template>
  </BasicLayout>
</template>

<script>
import { addTPushLog, delTPushLog, getTPushLog, listTPushLog, updateTPushLog } from '@/api/business/pushLog'
import ChartCard from '@/components/ChartCard'
import Trend from '@/components/Trend'
// import MiniArea from '@/components/MiniArea'

export default {
  name: 'TPushLog',
  components: {
    ChartCard,
    Trend
    // ,MiniArea
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      totalOk: 0,
      totalSum: 0,
      rateSucc: 0,
      rateFail: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      tPushLogList: [],
      pushTypeOptions: [], pushIdOptions: [],
      // 关系表类型
      // 日期范围
      pickerOptions: {},
      dateRange: [],

      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        uid: undefined,
        lang: undefined,
        pushType: undefined,
        pushId: undefined,
        timeStampValue: undefined,
        pushAll: false
      },
      // 表单参数
      form: {
      },
      // 表单校验
      rules: { uid: [{ required: true, message: '用户ID不能为空', trigger: 'blur' }],
        lang: [{ required: true, message: '版本语言不能为空', trigger: 'blur' }],
        pushType: [{ required: true, message: 'PushType不能为空', trigger: 'blur' }],
        pushId: [{ required: true, message: 'PushId不能为空', trigger: 'blur' }],
        timeStampValue: [{ required: true, message: 'timeStampValue不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getListAll()
    this.getList()
    this.getDicts('push_type').then(response => {
      this.pushTypeOptions = response.data
    })
    this.getDicts('push_id').then(response => {
      this.pushIdOptions = response.data
    })
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      this.queryParams.pushAll = false
      console.log(this.queryParams)
      listTPushLog(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        console.log(response.data)

        this.total = response.data.count
        this.tPushLogList = response.data.list
        // console.log(response.data)
        this.loading = false
      }
      )
    },
    getListAll() {
      this.loading = true
      this.queryParams.pushAll = true
      listTPushLog(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        console.log(response.data)
        this.totalOk = response.data.list.PushOkNum
        this.totalSum = response.data.list.PushAllNum

        this.rateSucc = parseFloat(this.totalOk / this.totalSum * 100).toFixed(2)
        this.rateFail = parseFloat((this.totalSum - this.totalOk) / this.totalSum * 100).toFixed(2)
        this.loading = false
      }
      )
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {

        id: undefined,
        planetId: undefined,
        uid: undefined,
        lang: undefined,
        pushType: undefined,
        pushId: undefined,
        pushTotalNum: undefined,
        pushOkNum: undefined,
        pushFailNum: undefined,
        pushErrorMsg: undefined,
        timeStampValue: undefined
      }
      this.resetForm('form')
    },
    getImgList: function() {
      this.form[this.fileIndex] = this.$refs['fileChoose'].resultList[0].fullUrl
    },
    fileClose: function() {
      this.fileOpen = false
    },
    pushTypeFormat(row) {
      return this.selectDictLabel(this.pushTypeOptions, row.pushType)
    },
    pushIdFormat(row) {
      return this.selectDictLabel(this.pushIdOptions, row.pushId)
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
      this.getListAll()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加TPushLog'
      this.isEdit = false
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id =
              row.id || this.ids
      getTPushLog(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改TPushLog'
        this.isEdit = true
      })
    },
    /** 详细按钮操作 */
    handleView(row) {
      this.open = true
      this.form = row
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id !== undefined) {
            updateTPushLog(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          } else {
            addTPushLog(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var Ids = (row.id && [row.id]) || this.ids

      this.$confirm('是否确认删除编号为"' + Ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delTPushLog({ 'ids': Ids })
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function() {
      })
    }
  }
}
</script>
