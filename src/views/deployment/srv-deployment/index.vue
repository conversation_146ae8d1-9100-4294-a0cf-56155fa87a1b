<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card-head">
        <div slot="header" size="small" class="clearfix">
          <span>
            <el-button-group>
              <el-button type="primary" icon="el-icon-video-play" @click="startTick">开启</el-button>
              <el-button type="warning" icon="el-icon-video-pause" @click="stopTick">停止</el-button>
            </el-button-group>
          </span>
          zkUrl:{{ zkUrl }}
        </div>
        <div class="box-card">
          <slot>({{ refreshCount }})秒后刷新</slot>
          <el-collapse v-model="activeName">
            <el-collapse-item v-for="(item, index) in list" :key="item.name" :title="item.name" :name="index">
              <el-table
                :data="item.node"
                border
                style="width: 100%"
              >
                <el-table-column
                  fixed
                  prop="http_addr"
                  label="HTTP地址"
                />
                <el-table-column
                  prop="tcp_addr"
                  label="tcp地址"
                />
                <el-table-column
                  prop="node_color"
                  label="颜色"
                >
                  <template slot-scope="scope">
                    <el-tag
                      disable-transitions
                    > {{ getNodeColorContent(scope.row.node_color) }} </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  fixed="right"
                  label="操作"
                >
                  <template slot-scope="scope">
                    <el-button type="text" size="small" @click="handlerNodeSetColor(scope.row)">{{ getNodeSetColorContent(scope.row.node_color) }}</el-button>
                    <el-button type="text" size="small" @click="handlerNodeStop(scope.row)">停止</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-collapse-item>
          </el-collapse>
        </div>
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { nodeList, nodeSetColor, nodeStop } from '@/api/admin/srv-deployment'
import { listTEnv } from '@/api/admin/env'
import store from '../../../store'

export default {
  data() {
    return {
      activeName: [],
      list: [
      ],
      refreshCount: 5,
      zkUrl: null,
      timer: null
    }
  },
  created() {
    console.log('store', store.getters.project)
    var env = store.getters.project[1]
    var planetID = store.getters.project[2]
    var query = {}
    query.planetId = planetID
    listTEnv(query).then(response => {
      var envList = response.data.list
      for (let index = 0; index < envList.length; index++) {
        const element = envList[index]
        if (element.envType === env) {
          console.log('zk_addr', element.zk_addr)
          this.zkUrl = element.zk_addr
        }
      }
    }
    )
    // this.getNodeList()
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll)
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    getNodeList() {
      console.log('getNodeList')
      nodeList().then(respones => {
        // this.list = respones.data
        const srvList = []
        console.log(respones.data)
        for (const srvName in respones.data) {
          const item = { name: srvName, node: respones.data[srvName] }
          srvList.push(item)
        }
        console.log(srvList)
        this.list = srvList
      })
    },
    startTick() {
      this.refreshTicker()
    },
    stopTick() {
      if (this.timer) {
        clearInterval(this.timer)
      }
    },
    refreshTicker() {
      this.timer = setInterval((_) => {
        this.refreshCount -= 1
        if (this.refreshCount <= 0) {
          this.getNodeList()
          this.refreshCount = 5
        }
      }, 1000)
    },
    getNodeColorContent(nodeColor) {
      if (nodeColor === 0) {
        return '正常'
      } else {
        return '灰度'
      }
    },
    getNodeSetColorContent(nodeColor) {
      if (nodeColor === 0) {
        return '改为灰度'
      } else {
        return '改为正常'
      }
    },
    handlerNodeStop(node) {
      const param = { srvName: node.srv_name, tcpAddr: node.tcp_addr }
      const vObj = this
      this.$confirm('是否确认将服务:' + node.srv_name + ' 地址:' + node.tcp_addr + '停止? 停止之后请在1分钟之后再尝试重启！！', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        nodeStop(param).then(respones => {
          console.log('node stop', respones)
          vObj.getNodeList()
        })
      })
    },
    handlerNodeSetColor(node) {
      const param = { srvName: node.srv_name, tcpAddr: node.tcp_addr, color: 1 }
      let newColorCt = '灰度'
      if (node.node_color === 1) {
        param.color = 0
        newColorCt = '正常'
      }
      const vObj = this
      this.$confirm('是否确认将服务:' + node.srv_name + ' 地址:' + node.tcp_addr + '染色改为:' + newColorCt + '?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        nodeSetColor(param).then(respones => {
          console.log('node set color', respones)
          vObj.getNodeList()
        })
      })
    }
  }
}
</script>

<style>
.srv_dp {
  width: 90%;
  margin: 0 auto;
  padding: 30px 30px;
  background: #FFFFFF;
}
</style>
