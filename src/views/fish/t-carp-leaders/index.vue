
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="主键id" prop="id"><el-input
            v-model="queryParams.id"
            placeholder="请输入主键id"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="类别" prop="category"><el-input
            v-model="queryParams.category"
            placeholder="请输入类别"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="常驻" prop="resident"><el-input
            v-model="queryParams.resident"
            placeholder="请输入常驻"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="类名中文" prop="cnTaxon"><el-input
            v-model="queryParams.cnTaxon"
            placeholder="请输入类名中文"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="类名英文" prop="enTaxon"><el-input
            v-model="queryParams.enTaxon"
            placeholder="请输入类名英文"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="名称英文" prop="enName"><el-input
            v-model="queryParams.enName"
            placeholder="请输入名称英文"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="品牌" prop="brand"><el-input
            v-model="queryParams.brand"
            placeholder="请输入品牌"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="测试重量（kg）" prop="testWeight"><el-input
            v-model="queryParams.testWeight"
            placeholder="请输入测试重量（kg）"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="长度（m）" prop="length"><el-input
            v-model="queryParams.length"
            placeholder="请输入长度（m）"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="颜色" prop="color"><el-input
            v-model="queryParams.color"
            placeholder="请输入颜色"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="数量" prop="quantity"><el-input
            v-model="queryParams.quantity"
            placeholder="请输入数量"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="解锁等级" prop="requiredLevel"><el-input
            v-model="queryParams.requiredLevel"
            placeholder="请输入解锁等级"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="货币" prop="money"><el-input
            v-model="queryParams.money"
            placeholder="请输入货币"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="饵币" prop="baitCoin"><el-input
            v-model="queryParams.baitCoin"
            placeholder="请输入饵币"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="俱乐部币" prop="clubCurrency"><el-input
            v-model="queryParams.clubCurrency"
            placeholder="请输入俱乐部币"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="描述" prop="description"><el-input
            v-model="queryParams.description"
            placeholder="请输入描述"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="描述翻译" prop="cnDescriptive"><el-input
            v-model="queryParams.cnDescriptive"
            placeholder="请输入描述翻译"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              v-permisaction="['fish:tCarpLeaders:add']"
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-permisaction="['fish:tCarpLeaders:edit']"
              type="success"
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
            >修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-permisaction="['fish:tCarpLeaders:remove']"
              type="danger"
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
            >删除
            </el-button>
          </el-col>
        </el-row>

        <el-table v-loading="loading" :data="tCarpLeadersList" @selection-change="handleSelectionChange" @sort-change="handleSortChange">
          <el-table-column type="selection" width="55" align="center" /><el-table-column
            label="主键id"
            align="center"
            prop="id"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="类别"
            align="center"
            prop="category"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="常驻"
            align="center"
            prop="resident"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="类名中文"
            align="center"
            prop="cnTaxon"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="类名英文"
            align="center"
            prop="enTaxon"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="图片地址"
            width="200"
            align="center"
            prop="imageUrl"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <!-- 使用el-popover实现点击放大 -->
              <el-popover trigger="hover" placement="bottom">
                <el-image
                  style="width: 100%; height: auto;"
                  :src="scope.row.imageUrl"
                />

                <!-- 触发el-popover显示的小图片 -->
                <img
                  slot="reference"
                  :src="scope.row.imageUrl"
                  class="thumbnail"
                  @mouseenter="handleMouseEnter(scope.row.imageUrl)"
                >
              </el-popover>

            </template>
          </el-table-column>
          <el-table-column
            label="名称英文"
            align="center"
            prop="enName"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="品牌"
            align="center"
            prop="brand"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="测试重量（kg）"
            align="center"
            prop="testWeight"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="长度（m）"
            align="center"
            prop="length"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="颜色"
            align="center"
            prop="color"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="数量"
            align="center"
            prop="quantity"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="解锁等级"
            align="center"
            prop="requiredLevel"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="货币"
            align="center"
            prop="money"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="饵币"
            align="center"
            prop="baitCoin"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="俱乐部币"
            align="center"
            prop="clubCurrency"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="描述"
            align="center"
            prop="description"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="描述翻译"
            align="center"
            prop="cnDescriptive"
            :show-overflow-tooltip="true"
            sortable="custom"
          />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要修改吗?"
                confirm-button-text="修改"
                @onConfirm="handleUpdate(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['fish:tCarpLeaders:edit']"
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                >修改
                </el-button>
              </el-popconfirm>
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要删除吗?"
                confirm-button-text="删除"
                @onConfirm="handleDelete(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['fish:tCarpLeaders:remove']"
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                >删除
                </el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          :page-sizes="[100]"
          @pagination="getList"
        />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px">
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">

            <el-form-item label="类别" prop="category">
              <el-input
                v-model="form.category"
                placeholder="类别"
              />
            </el-form-item>
            <el-form-item label="常驻" prop="resident">
              <el-input
                v-model="form.resident"
                placeholder="常驻"
              />
            </el-form-item>
            <el-form-item label="类名中文" prop="cnTaxon">
              <el-input
                v-model="form.cnTaxon"
                placeholder="类名中文"
              />
            </el-form-item>
            <el-form-item label="类名英文" prop="enTaxon">
              <el-input
                v-model="form.enTaxon"
                placeholder="类名英文"
              />
            </el-form-item>
            <el-form-item label="图片地址" prop="imageUrl">
              <el-input
                v-model="form.imageUrl"
                placeholder="图片地址"
              />
            </el-form-item>
            <el-form-item label="名称英文" prop="enName">
              <el-input
                v-model="form.enName"
                placeholder="名称英文"
              />
            </el-form-item>
            <el-form-item label="品牌" prop="brand">
              <el-input
                v-model="form.brand"
                placeholder="品牌"
              />
            </el-form-item>
            <el-form-item label="测试重量（kg）" prop="testWeight">
              <el-input
                v-model="form.testWeight"
                placeholder="测试重量（kg）"
              />
            </el-form-item>
            <el-form-item label="长度（m）" prop="length">
              <el-input
                v-model="form.length"
                placeholder="长度（m）"
              />
            </el-form-item>
            <el-form-item label="颜色" prop="color">
              <el-input
                v-model="form.color"
                placeholder="颜色"
              />
            </el-form-item>
            <el-form-item label="数量" prop="quantity">
              <el-input
                v-model="form.quantity"
                placeholder="数量"
              />
            </el-form-item>
            <el-form-item label="解锁等级" prop="requiredLevel">
              <el-input
                v-model="form.requiredLevel"
                placeholder="解锁等级"
              />
            </el-form-item>
            <el-form-item label="货币" prop="money">
              <el-input
                v-model="form.money"
                placeholder="货币"
              />
            </el-form-item>
            <el-form-item label="饵币" prop="baitCoin">
              <el-input
                v-model="form.baitCoin"
                placeholder="饵币"
              />
            </el-form-item>
            <el-form-item label="俱乐部币" prop="clubCurrency">
              <el-input
                v-model="form.clubCurrency"
                placeholder="俱乐部币"
              />
            </el-form-item>
            <el-form-item label="描述" prop="description">
              <el-input
                v-model="form.description"
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
              />
            </el-form-item>
            <el-form-item label="描述翻译" prop="cnDescriptive">
              <el-input
                v-model="form.cnDescriptive"
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { addTCarpLeaders, delTCarpLeaders, getTCarpLeaders, listTCarpLeaders, updateTCarpLeaders } from '@/api/fish/t-carp-leaders'

export default {
  name: 'TCarpLeaders',
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      tCarpLeadersList: [],

      // 关系表类型

      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 100,
        id: undefined,
        category: undefined,
        resident: undefined,
        cnTaxon: undefined,
        enTaxon: undefined,
        imageUrl: undefined,
        enName: undefined,
        brand: undefined,
        testWeight: undefined,
        length: undefined,
        color: undefined,
        quantity: undefined,
        requiredLevel: undefined,
        money: undefined,
        baitCoin: undefined,
        clubCurrency: undefined,
        description: undefined,
        cnDescriptive: undefined,
        createName: undefined

      },
      // 表单参数
      form: {
      },
      // 表单校验
      rules: { id: [{ required: true, message: '主键id不能为空', trigger: 'blur' }],
        category: [{ required: true, message: '类别不能为空', trigger: 'blur' }],
        resident: [{ required: true, message: '常驻不能为空', trigger: 'blur' }],
        cnTaxon: [{ required: true, message: '类名中文不能为空', trigger: 'blur' }],
        enTaxon: [{ required: true, message: '类名英文不能为空', trigger: 'blur' }],
        enName: [{ required: true, message: '名称英文不能为空', trigger: 'blur' }],
        brand: [{ required: true, message: '品牌不能为空', trigger: 'blur' }],
        testWeight: [{ required: true, message: '测试重量（kg）不能为空', trigger: 'blur' }],
        length: [{ required: true, message: '长度（m）不能为空', trigger: 'blur' }],
        color: [{ required: true, message: '颜色不能为空', trigger: 'blur' }],
        quantity: [{ required: true, message: '数量不能为空', trigger: 'blur' }],
        requiredLevel: [{ required: true, message: '解锁等级不能为空', trigger: 'blur' }],
        money: [{ required: true, message: '货币不能为空', trigger: 'blur' }],
        baitCoin: [{ required: true, message: '饵币不能为空', trigger: 'blur' }],
        clubCurrency: [{ required: true, message: '俱乐部币不能为空', trigger: 'blur' }],
        description: [{ required: true, message: '描述不能为空', trigger: 'blur' }],
        cnDescriptive: [{ required: true, message: '描述翻译不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      listTCarpLeaders(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.tCarpLeadersList = response.data.list
        this.total = response.data.count
        this.loading = false
      }
      )
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {

        id: undefined,
        category: undefined,
        resident: undefined,
        cnTaxon: undefined,
        enTaxon: undefined,
        imageUrl: undefined,
        enName: undefined,
        brand: undefined,
        testWeight: undefined,
        length: undefined,
        color: undefined,
        quantity: undefined,
        requiredLevel: undefined,
        money: undefined,
        baitCoin: undefined,
        clubCurrency: undefined,
        description: undefined,
        cnDescriptive: undefined
      }
      this.resetForm('form')
    },
    getImgList: function() {
      this.form[this.fileIndex] = this.$refs['fileChoose'].resultList[0].fullUrl
    },
    fileClose: function() {
      this.fileOpen = false
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加鲤鱼前导线'
      this.isEdit = false
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id =
                row.id || this.ids
      getTCarpLeaders(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改鲤鱼前导线'
        this.isEdit = true
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id !== undefined) {
            updateTCarpLeaders(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          } else {
            addTCarpLeaders(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          }
        }
      })
    },
    handleSortChange(sort) {
      // 假设我们有一个可以正确提取数字的函数 extractFirstNumber
      const extractFirstNumber = (value) => {
        // 这里提取数字或者浮点数
        if (typeof value === 'string') {
          const matches = value.matchAll(/\d+(\.\d+)?/g)
          return Array.from(matches, m => m[0]).map(num => num.includes('.') ? parseFloat(num) : parseInt(num))
        }
        // 如果不是字符串或没有找到数字，则直接返回原值
        return value
      }

      // 创建新的排序函数
      const sortFunction = (a, b) => {
        const valueA = a[sort.prop]
        const valueB = b[sort.prop]

        const intValueA = extractFirstNumber(valueA)
        const intValueB = extractFirstNumber(valueB)

        console.log('handleSortChange ascending:', intValueA, intValueB, typeof intValueA, typeof intValueB)

        if (Array.isArray(intValueA) && Array.isArray(intValueB)) {
          for (let i = 0; i < Math.min(intValueA.length, intValueB.length); i++) {
            if (intValueA[i] !== intValueB[i]) {
              // 比较当前索引的数字，决定排序
              return sort.order === 'ascending' ? intValueA[i] - intValueB[i] : intValueB[i] - intValueA[i]
            }
          }
        }

        return sort.order === 'ascending' ? valueA.localeCompare(valueB, undefined, { numeric: true }) : valueB.localeCompare(valueA, undefined, { numeric: true })
      }

      // 使用数组的 slice 方法来创建一个新数组，避免直接修改原数组
      this.tCarpLeadersList = [...this.tCarpLeadersList].sort(sortFunction)
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var Ids = (row.id && [row.id]) || this.ids

      this.$confirm('是否确认删除编号为"' + Ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delTCarpLeaders({ 'ids': Ids })
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function() {
      })
    }
  }
}
</script>
