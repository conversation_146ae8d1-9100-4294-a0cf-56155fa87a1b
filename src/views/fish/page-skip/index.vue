<template>
  <div>
    <el-link type="primary" :href="mapUrl" target="_blank">点击访问 {{ mapUrlName }}</el-link>
  </div>
</template>

<script>
export default {
  data() {
    return {
      mapUrl: 'https://************:8081/fishmap/', // 地图编辑器网址
      mapUrlName: '地图编辑器' // 地图编辑器名称
    }
  }
}
</script>

<style scoped>
.custom-link {
  color: #007bff; /* 自定义颜色 */
  font-weight: bold;
  text-decoration: none; /* 移除下划线 */
  transition: all 0.3s; /* 添加过渡效果 */
}

.custom-link:hover {
  color: #0056b3; /* 鼠标悬停时的颜色变化 */
  text-decoration: underline; /* 悬停时添加下划线 */
}
</style>
