
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="主键id" prop="id"><el-input
            v-model="queryParams.id"
            placeholder="请输入主键id"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="场景" prop="scene"><el-input
            v-model="queryParams.scene"
            placeholder="请输入场景"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="解锁等级" prop="requiredLevel"><el-input
            v-model="queryParams.requiredLevel"
            placeholder="请输入解锁等级"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="旅行费" prop="travelFee"><el-input
            v-model="queryParams.travelFee"
            placeholder="请输入旅行费"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="钓鱼费(per_day)" prop="fishingFee"><el-input
            v-model="queryParams.fishingFee"
            placeholder="请输入钓鱼费(per_day)"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="基本执照" prop="basicLicenseReleased"><el-input
            v-model="queryParams.basicLicenseReleased"
            placeholder="请输入基本执照"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="高级执照" prop="advancedLicenseReleased"><el-input
            v-model="queryParams.advancedLicenseReleased"
            placeholder="请输入高级执照"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="鱼英文" prop="enFish"><el-input
            v-model="queryParams.enFish"
            placeholder="请输入鱼英文"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="鱼中文" prop="cnFish"><el-input
            v-model="queryParams.cnFish"
            placeholder="请输入鱼中文"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="鱼规则" prop="speciesRule"><el-input
            v-model="queryParams.speciesRule"
            placeholder="请输入鱼规则"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="比赛列表" prop="gameList"><el-input
            v-model="queryParams.gameList"
            placeholder="请输入比赛列表"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              v-permisaction="['fish:tScene:add']"
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-permisaction="['fish:tScene:edit']"
              type="success"
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
            >修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-permisaction="['fish:tScene:remove']"
              type="danger"
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
            >删除
            </el-button>
          </el-col>
        </el-row>

        <el-table v-loading="loading" :data="tSceneList" @selection-change="handleSelectionChange" @sort-change="handleSortChange">
          <el-table-column type="selection" width="55" align="center" /><el-table-column
            label="主键id"
            align="center"
            prop="id"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="场景"
            align="center"
            prop="scene"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="解锁等级"
            align="center"
            prop="requiredLevel"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="旅行费"
            align="center"
            prop="travelFee"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="钓鱼费(per_day)"
            align="center"
            prop="fishingFee"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="基本执照"
            align="center"
            prop="basicLicenseReleased"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="高级执照"
            align="center"
            prop="advancedLicenseReleased"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="鱼英文"
            align="center"
            prop="enFish"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="鱼中文"
            align="center"
            prop="cnFish"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="鱼规则"
            align="center"
            prop="speciesRule"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="比赛列表"
            align="center"
            prop="gameList"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="场景图片"
            width="200"
            align="center"
            prop="sceneUrl"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <!-- 使用el-popover实现点击放大 -->
              <el-popover trigger="hover" placement="bottom">
                <el-image
                  style="width: 100%; height: auto;"
                  :src="scope.row.sceneUrl"
                />

                <!-- 触发el-popover显示的小图片 -->
                <img
                  slot="reference"
                  :src="scope.row.sceneUrl"
                  class="thumbnail"
                  @mouseenter="handleMouseEnter(scope.row.sceneUrl)"
                >
              </el-popover>

            </template>
          </el-table-column>
          <el-table-column
            label="天气图片"
            width="200"
            align="center"
            prop="weatherUrl"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <!-- 使用el-popover实现点击放大 -->
              <el-popover trigger="hover" placement="bottom">
                <el-image
                  style="width: 100%; height: auto;"
                  :src="scope.row.weatherUrl"
                />
                <!-- 触发el-popover显示的小图片 -->
                <img
                  slot="reference"
                  :src="scope.row.weatherUrl"
                  class="thumbnail"
                  @mouseenter="handleMouseEnter(scope.row.weatherUrl)"
                >
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column
            label="基本执照图片"
            width="200"
            align="center"
            prop="basicLicenseReleasedUrl"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <!-- 使用el-popover实现点击放大 -->
              <el-popover trigger="hover" placement="bottom">
                <el-image
                  style="width: 100%; height: auto;"
                  :src="scope.row.basicLicenseReleasedUrl"
                />

                <!-- 触发el-popover显示的小图片 -->
                <img
                  slot="reference"
                  :src="scope.row.basicLicenseReleasedUrl"
                  class="thumbnail"
                  @mouseenter="handleMouseEnter(scope.row.basicLicenseReleasedUrl)"
                >
              </el-popover>

            </template>
          </el-table-column>
          <el-table-column
            label="高级执照图片"
            width="200"
            align="center"
            prop="advancedLicenseReleasedUrl"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <!-- 使用el-popover实现点击放大 -->
              <el-popover trigger="hover" placement="bottom">
                <el-image
                  style="width: 100%; height: auto;"
                  :src="scope.row.advancedLicenseReleasedUrl"
                />

                <!-- 触发el-popover显示的小图片 -->
                <img
                  slot="reference"
                  :src="scope.row.advancedLicenseReleasedUrl"
                  class="thumbnail"
                  @mouseenter="handleMouseEnter(scope.row.advancedLicenseReleasedUrl)"
                >
              </el-popover>

            </template>
          </el-table-column>

          <el-table-column
            label="创建者名称"
            align="center"
            prop="createName"
            :show-overflow-tooltip="true"
            sortable="custom"
          />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要修改吗?"
                confirm-button-text="修改"
                @onConfirm="handleUpdate(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['fish:tScene:edit']"
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                >修改
                </el-button>
              </el-popconfirm>
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要删除吗?"
                confirm-button-text="删除"
                @onConfirm="handleDelete(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['fish:tScene:remove']"
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                >删除
                </el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          :page-sizes="[100]"
          @pagination="getList"
        />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px">
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">

            <el-form-item label="场景" prop="scene">
              <el-input
                v-model="form.scene"
                placeholder="场景"
              />
            </el-form-item>
            <el-form-item label="解锁等级" prop="requiredLevel">
              <el-input
                v-model="form.requiredLevel"
                placeholder="解锁等级"
              />
            </el-form-item>
            <el-form-item label="旅行费" prop="travelFee">
              <el-input
                v-model="form.travelFee"
                placeholder="旅行费"
              />
            </el-form-item>
            <el-form-item label="钓鱼费(per_day)" prop="fishingFee">
              <el-input
                v-model="form.fishingFee"
                placeholder="钓鱼费(per_day)"
              />
            </el-form-item>
            <el-form-item label="基本执照" prop="basicLicenseReleased">
              <el-input
                v-model="form.basicLicenseReleased"
                placeholder="基本执照"
              />
            </el-form-item>
            <el-form-item label="高级执照" prop="advancedLicenseReleased">
              <el-input
                v-model="form.advancedLicenseReleased"
                placeholder="高级执照"
              />
            </el-form-item>
            <el-form-item label="鱼英文" prop="enFish">
              <el-input
                v-model="form.enFish"
                placeholder="鱼英文"
              />
            </el-form-item>
            <el-form-item label="鱼中文" prop="cnFish">
              <el-input
                v-model="form.cnFish"
                placeholder="鱼中文"
              />
            </el-form-item>
            <el-form-item label="鱼规则" prop="speciesRule">
              <el-input
                v-model="form.speciesRule"
                placeholder="鱼规则"
              />
            </el-form-item>
            <el-form-item label="比赛列表" prop="gameList">
              <el-input
                v-model="form.gameList"
                placeholder="比赛列表"
              />
            </el-form-item>
            <el-form-item label="场景图片" prop="sceneUrl">
              <el-input
                v-model="form.sceneUrl"
                placeholder="场景图片"
              />
            </el-form-item>
            <el-form-item label="天气图片" prop="weatherUrl">
              <el-input
                v-model="form.weatherUrl"
                placeholder="天气图片"
              />
            </el-form-item>
            <el-form-item label="基本执照图片" prop="basicLicenseReleasedUrl">
              <el-input
                v-model="form.basicLicenseReleasedUrl"
                placeholder="基本执照图片"
              />
            </el-form-item>
            <el-form-item label="高级执照图片" prop="advancedLicenseReleasedUrl">
              <el-input
                v-model="form.advancedLicenseReleasedUrl"
                placeholder="高级执照图片"
              />
            </el-form-item>
            <el-form-item label="创建者名称" prop="createName">
              <el-input
                v-model="form.createName"
                placeholder="创建者名称"
              />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { addTScene, delTScene, getTScene, listTScene, updateTScene } from '@/api/fish/t-scene'

export default {
  name: 'TScene',
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      tSceneList: [],

      // 关系表类型

      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 100,
        id: undefined,
        scene: undefined,
        requiredLevel: undefined,
        travelFee: undefined,
        fishingFee: undefined,
        basicLicenseReleased: undefined,
        advancedLicenseReleased: undefined,
        enFish: undefined,
        cnFish: undefined,
        speciesRule: undefined,
        gameList: undefined,
        sceneUrl: undefined,
        weatherUrl: undefined,
        basicLicenseReleasedUrl: undefined,
        advancedLicenseReleasedUrl: undefined,
        createName: undefined

      },
      // 表单参数
      form: {
      },
      // 表单校验
      rules: { id: [{ required: true, message: '主键id不能为空', trigger: 'blur' }],
        scene: [{ required: true, message: '场景不能为空', trigger: 'blur' }],
        requiredLevel: [{ required: true, message: '解锁等级不能为空', trigger: 'blur' }],
        travelFee: [{ required: true, message: '旅行费不能为空', trigger: 'blur' }],
        fishingFee: [{ required: true, message: '钓鱼费(per_day)不能为空', trigger: 'blur' }],
        basicLicenseReleased: [{ required: true, message: '基本执照不能为空', trigger: 'blur' }],
        advancedLicenseReleased: [{ required: true, message: '高级执照不能为空', trigger: 'blur' }],
        enFish: [{ required: true, message: '鱼英文不能为空', trigger: 'blur' }],
        cnFish: [{ required: true, message: '鱼中文不能为空', trigger: 'blur' }],
        speciesRule: [{ required: true, message: '鱼规则不能为空', trigger: 'blur' }],
        gameList: [{ required: true, message: '比赛列表不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      listTScene(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.tSceneList = response.data.list
        this.total = response.data.count
        this.loading = false
      }
      )
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {

        id: undefined,
        scene: undefined,
        requiredLevel: undefined,
        travelFee: undefined,
        fishingFee: undefined,
        basicLicenseReleased: undefined,
        advancedLicenseReleased: undefined,
        enFish: undefined,
        cnFish: undefined,
        speciesRule: undefined,
        gameList: undefined,
        sceneUrl: undefined,
        weatherUrl: undefined,
        basicLicenseReleasedUrl: undefined,
        advancedLicenseReleasedUrl: undefined,
        createName: undefined
      }
      this.resetForm('form')
    },
    getImgList: function() {
      this.form[this.fileIndex] = this.$refs['fileChoose'].resultList[0].fullUrl
    },
    fileClose: function() {
      this.fileOpen = false
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加场景'
      this.isEdit = false
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id =
                row.id || this.ids
      getTScene(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改场景'
        this.isEdit = true
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id !== undefined) {
            updateTScene(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          } else {
            addTScene(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          }
        }
      })
    },
    handleSortChange(sort) {
      // 假设我们有一个可以正确提取数字的函数 extractFirstNumber
      const extractFirstNumber = (value) => {
        // 这里提取数字或者浮点数
        if (typeof value === 'string') {
          const matches = value.matchAll(/\d+(\.\d+)?/g)
          return Array.from(matches, m => m[0]).map(num => num.includes('.') ? parseFloat(num) : parseInt(num))
        }
        // 如果不是字符串或没有找到数字，则直接返回原值
        return value
      }

      // 创建新的排序函数
      const sortFunction = (a, b) => {
        const valueA = a[sort.prop]
        const valueB = b[sort.prop]

        const intValueA = extractFirstNumber(valueA)
        const intValueB = extractFirstNumber(valueB)

        console.log('handleSortChange ascending:', intValueA, intValueB, typeof intValueA, typeof intValueB)

        if (Array.isArray(intValueA) && Array.isArray(intValueB)) {
          for (let i = 0; i < Math.min(intValueA.length, intValueB.length); i++) {
            if (intValueA[i] !== intValueB[i]) {
              // 比较当前索引的数字，决定排序
              return sort.order === 'ascending' ? intValueA[i] - intValueB[i] : intValueB[i] - intValueA[i]
            }
          }
        }

        return sort.order === 'ascending' ? valueA.localeCompare(valueB, undefined, { numeric: true }) : valueB.localeCompare(valueA, undefined, { numeric: true })
      }

      // 使用数组的 slice 方法来创建一个新数组，避免直接修改原数组
      this.tSceneList = [...this.tSceneList].sort(sortFunction)
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var Ids = (row.id && [row.id]) || this.ids

      this.$confirm('是否确认删除编号为"' + Ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delTScene({ 'ids': Ids })
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function() {
      })
    }
  }
}
</script>
