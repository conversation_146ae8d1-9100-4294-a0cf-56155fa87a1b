
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="主键id" prop="id"><el-input
            v-model="queryParams.id"
            placeholder="请输入主键id"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="英文名称" prop="enName"><el-input
            v-model="queryParams.enName"
            placeholder="请输入英文名称"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="中文名称" prop="cnName"><el-input
            v-model="queryParams.cnName"
            placeholder="请输入中文名称"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="种类" prop="species"><el-input
            v-model="queryParams.species"
            placeholder="请输入种类"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="wiki图片地址" prop="wikiImageUrl"><el-input
            v-model="queryParams.wikiImageUrl"
            placeholder="请输入wiki图片地址"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="规则" prop="rule"><el-input
            v-model="queryParams.rule"
            placeholder="请输入规则"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="重量(max)" prop="weight"><el-input
            v-model="queryParams.weight"
            placeholder="请输入重量(max)"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="单价(kg)" prop="reward"><el-input
            v-model="queryParams.reward"
            placeholder="请输入单价(kg)"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="长度(cm)" prop="length"><el-input
            v-model="queryParams.length"
            placeholder="请输入长度(cm)"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="诱饵" prop="baits"><el-input
            v-model="queryParams.baits"
            placeholder="请输入诱饵"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="诱饵翻译" prop="cnBaits"><el-input
            v-model="queryParams.cnBaits"
            placeholder="请输入诱饵翻译"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="假饵" prop="lures"><el-input
            v-model="queryParams.lures"
            placeholder="请输入假饵"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="假饵翻译" prop="cnLures"><el-input
            v-model="queryParams.cnLures"
            placeholder="请输入假饵翻译"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="出没" prop="haunt"><el-input
            v-model="queryParams.haunt"
            placeholder="请输入出没"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="场景" prop="scene"><el-input
            v-model="queryParams.scene"
            placeholder="请输入场景"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="区域" prop="area"><el-input
            v-model="queryParams.area"
            placeholder="请输入区域"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="水域" prop="water"><el-input
            v-model="queryParams.water"
            placeholder="请输入水域"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="食物" prop="food"><el-input
            v-model="queryParams.food"
            placeholder="请输入食物"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="特征" prop="feature"><el-input
            v-model="queryParams.feature"
            placeholder="请输入特征"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              v-permisaction="['fish:tFishInformation:add']"
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-permisaction="['fish:tFishInformation:edit']"
              type="success"
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
            >修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-permisaction="['fish:tFishInformation:remove']"
              type="danger"
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
            >删除
            </el-button>
          </el-col>
        </el-row>

        <el-table v-loading="loading" :data="tFishInformationList" @selection-change="handleSelectionChange" @sort-change="handleSortChange">
          <el-table-column type="selection" width="55" align="center" /><el-table-column
            label="主键id"
            align="center"
            prop="id"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="英文名称"
            width="120"
            align="center"
            prop="enName"
            :show-overflow-tooltip="true"
            sortable="custom"
          />
          <el-table-column
            label="中文名称"
            width="120"
            align="center"
            prop="cnName"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="种类"
            width="200"
            align="center"
            prop="species"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="图片地址"
            width="200"
            align="center"
            prop="imageUrl"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <!-- 使用el-popover实现点击放大 -->
              <el-popover trigger="hover" placement="bottom">
                <el-image
                  style="width: 100%; height: auto;"
                  :src="scope.row.imageUrl"
                />

                <!-- 触发el-popover显示的小图片 -->
                <img
                  slot="reference"
                  :src="scope.row.imageUrl"
                  class="thumbnail"
                  @mouseenter="handleMouseEnter(scope.row.imageUrl)"
                >
              </el-popover>

            </template>
          </el-table-column>
          <el-table-column
            label="wiki图片地址"
            align="center"
            prop="wikiImageUrl"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="规则"
            width="120"
            align="center"
            prop="rule"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="重量(max)"
            align="center"
            prop="weight"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="单价(kg)"
            align="center"
            prop="reward"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="长度(cm)"
            align="center"
            prop="length"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="诱饵"
            width="300"
            align="center"
            prop="baits"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="诱饵翻译"
            width="300"
            align="center"
            prop="cnBaits"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="假饵"
            align="center"
            prop="lures"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="假饵翻译"
            align="center"
            prop="cnLures"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="出没"
            align="center"
            prop="haunt"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="场景"
            align="center"
            prop="scene"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="区域"
            align="center"
            prop="area"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="水域"
            align="center"
            prop="water"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="食物"
            align="center"
            prop="food"
            :show-overflow-tooltip="true"
            sortable="custom"
          /><el-table-column
            label="特征"
            align="center"
            prop="feature"
            :show-overflow-tooltip="true"
            sortable="custom"
          />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要修改吗?"
                confirm-button-text="修改"
                @onConfirm="handleUpdate(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['fish:tFishInformation:edit']"
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                >修改
                </el-button>
              </el-popconfirm>
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要删除吗?"
                confirm-button-text="删除"
                @onConfirm="handleDelete(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['fish:tFishInformation:remove']"
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                >删除
                </el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          :page-sizes="[100]"
          @pagination="getList"
        />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px">
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">

            <el-form-item label="英文名称" prop="enName">
              <el-input
                v-model="form.enName"
                placeholder="英文名称"
              />
            </el-form-item>
            <el-form-item label="中文名称" prop="cnName">
              <el-input
                v-model="form.cnName"
                placeholder="中文名称"
              />
            </el-form-item>
            <el-form-item label="种类" prop="species">
              <el-input
                v-model="form.species"
                placeholder="种类"
              />
            </el-form-item>
            <el-form-item label="图片地址" prop="imageUrl">
              <el-input
                v-model="form.imageUrl"
                placeholder="图片地址"
              />
            </el-form-item>
            <el-form-item label="wiki图片地址" prop="wikiImageUrl">
              <el-input
                v-model="form.wikiImageUrl"
                placeholder="wiki图片地址"
              />
            </el-form-item>
            <el-form-item label="规则" prop="rule">
              <el-input
                v-model="form.rule"
                placeholder="规则"
              />
            </el-form-item>
            <el-form-item label="重量(max)" prop="weight">
              <el-input
                v-model="form.weight"
                placeholder="重量(max)"
              />
            </el-form-item>
            <el-form-item label="单价(kg)" prop="reward">
              <el-input
                v-model="form.reward"
                placeholder="单价(kg)"
              />
            </el-form-item>
            <el-form-item label="长度(cm)" prop="length">
              <el-input
                v-model="form.length"
                placeholder="长度(cm)"
              />
            </el-form-item>
            <el-form-item label="诱饵" prop="baits">
              <el-input
                v-model="form.baits"
                placeholder="诱饵"
              />
            </el-form-item>
            <el-form-item label="诱饵翻译" prop="cnBaits">
              <el-input
                v-model="form.cnBaits"
                placeholder="诱饵翻译"
              />
            </el-form-item>
            <el-form-item label="假饵" prop="lures">
              <el-input
                v-model="form.lures"
                placeholder="假饵"
              />
            </el-form-item>
            <el-form-item label="假饵翻译" prop="cnLures">
              <el-input
                v-model="form.cnLures"
                placeholder="假饵翻译"
              />
            </el-form-item>
            <el-form-item label="出没" prop="haunt">
              <el-input
                v-model="form.haunt"
                placeholder="出没"
              />
            </el-form-item>
            <el-form-item label="场景" prop="scene">
              <el-input
                v-model="form.scene"
                placeholder="场景"
              />
            </el-form-item>
            <el-form-item label="区域" prop="area">
              <el-input
                v-model="form.area"
                placeholder="区域"
              />
            </el-form-item>
            <el-form-item label="水域" prop="water">
              <el-input
                v-model="form.water"
                placeholder="水域"
              />
            </el-form-item>
            <el-form-item label="食物" prop="food">
              <el-input
                v-model="form.food"
                placeholder="食物"
              />
            </el-form-item>
            <el-form-item label="特征" prop="feature">
              <el-input
                v-model="form.feature"
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { addTFishInformation, delTFishInformation, getTFishInformation, listTFishInformation, updateTFishInformation } from '@/api/fish/t-fish-information'

export default {
  name: 'TFishInformation',
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      tFishInformationList: [],

      // 关系表类型

      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 100,
        id: undefined,
        enName: undefined,
        cnName: undefined,
        species: undefined,
        imageUrl: undefined,
        wikiImageUrl: undefined,
        rule: undefined,
        weight: undefined,
        reward: undefined,
        length: undefined,
        baits: undefined,
        cnBaits: undefined,
        lures: undefined,
        cnLures: undefined,
        haunt: undefined,
        scene: undefined,
        area: undefined,
        water: undefined,
        food: undefined,
        feature: undefined,
        createName: undefined

      },
      // 表单参数
      form: {
      },
      // 表单校验
      rules: { id: [{ required: true, message: '主键id不能为空', trigger: 'blur' }],
        enName: [{ required: true, message: '英文名称不能为空', trigger: 'blur' }],
        cnName: [{ required: true, message: '中文名称不能为空', trigger: 'blur' }],
        species: [{ required: true, message: '种类不能为空', trigger: 'blur' }],
        wikiImageUrl: [{ required: true, message: 'wiki图片地址不能为空', trigger: 'blur' }],
        rule: [{ required: true, message: '规则不能为空', trigger: 'blur' }],
        weight: [{ required: true, message: '重量(max)不能为空', trigger: 'blur' }],
        reward: [{ required: true, message: '单价(kg)不能为空', trigger: 'blur' }],
        length: [{ required: true, message: '长度(cm)不能为空', trigger: 'blur' }],
        baits: [{ required: true, message: '诱饵不能为空', trigger: 'blur' }],
        cnBaits: [{ required: true, message: '诱饵翻译不能为空', trigger: 'blur' }],
        lures: [{ required: true, message: '假饵不能为空', trigger: 'blur' }],
        cnLures: [{ required: true, message: '假饵翻译不能为空', trigger: 'blur' }],
        haunt: [{ required: true, message: '出没不能为空', trigger: 'blur' }],
        scene: [{ required: true, message: '场景不能为空', trigger: 'blur' }],
        area: [{ required: true, message: '区域不能为空', trigger: 'blur' }],
        water: [{ required: true, message: '水域不能为空', trigger: 'blur' }],
        food: [{ required: true, message: '食物不能为空', trigger: 'blur' }],
        feature: [{ required: true, message: '特征不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      listTFishInformation(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.tFishInformationList = response.data.list
        this.total = response.data.count
        this.loading = false
      }
      )
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {

        id: undefined,
        enName: undefined,
        cnName: undefined,
        species: undefined,
        imageUrl: undefined,
        wikiImageUrl: undefined,
        rule: undefined,
        weight: undefined,
        reward: undefined,
        length: undefined,
        baits: undefined,
        cnBaits: undefined,
        lures: undefined,
        cnLures: undefined,
        haunt: undefined,
        scene: undefined,
        area: undefined,
        water: undefined,
        food: undefined,
        feature: undefined
      }
      this.resetForm('form')
    },
    getImgList: function() {
      this.form[this.fileIndex] = this.$refs['fileChoose'].resultList[0].fullUrl
    },
    fileClose: function() {
      this.fileOpen = false
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加鱼信息'
      this.isEdit = false
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id =
                row.id || this.ids
      getTFishInformation(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改鱼信息'
        this.isEdit = true
      })
    },
    handleMouseEnter() {

    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id !== undefined) {
            updateTFishInformation(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          } else {
            addTFishInformation(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          }
        }
      })
    },
    handleSortChange(sort) {
      // 假设我们有一个可以正确提取数字的函数 extractFirstNumber
      const extractFirstNumber = (value) => {
        // 这里提取数字或者浮点数
        if (typeof value === 'string') {
          const matches = value.matchAll(/\d+(\.\d+)?/g)
          return Array.from(matches, m => m[0]).map(num => num.includes('.') ? parseFloat(num) : parseInt(num))
        }
        // 如果不是字符串或没有找到数字，则直接返回原值
        return value
      }

      // 创建新的排序函数
      const sortFunction = (a, b) => {
        const valueA = a[sort.prop]
        const valueB = b[sort.prop]

        const intValueA = extractFirstNumber(valueA)
        const intValueB = extractFirstNumber(valueB)

        console.log('handleSortChange ascending:', intValueA, intValueB, typeof intValueA, typeof intValueB)

        if (Array.isArray(intValueA) && Array.isArray(intValueB)) {
          for (let i = 0; i < Math.min(intValueA.length, intValueB.length); i++) {
            if (intValueA[i] !== intValueB[i]) {
              // 比较当前索引的数字，决定排序
              return sort.order === 'ascending' ? intValueA[i] - intValueB[i] : intValueB[i] - intValueA[i]
            }
          }
        }

        return sort.order === 'ascending' ? valueA.localeCompare(valueB, undefined, { numeric: true }) : valueB.localeCompare(valueA, undefined, { numeric: true })
      }

      // 使用数组的 slice 方法来创建一个新数组，避免直接修改原数组
      this.tFishInformationList = [...this.tFishInformationList].sort(sortFunction)
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      var Ids = (row.id && [row.id]) || this.ids

      this.$confirm('是否确认删除编号为"' + Ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delTFishInformation({ 'ids': Ids })
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function() {
      })
    }
  }
}
</script>

<style scoped>
.thumbnail {
  width: 50px;
  height: 50px;
  cursor: pointer;
}
.el-table {
  width: 100%;
}
</style>
