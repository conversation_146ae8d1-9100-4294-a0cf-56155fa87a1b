<template>
  <div>
    <BasicLayout>
      <template #wrapper>
        <el-row :gutter="20" class="mb10">
          <el-col :span="12" :sm="12" :md="12">
            <el-card v-if="info.code==200" class="box-card" style="width: 100%; margin-top: 5px;">
              <div slot="header" class="clearfix">
                <span>压测机器人程序 </span>
              </div>
              <div v-loading="loading" class="monitor">
                <Cell label="目标网关" :value="info.path" border />
                <Cell label="磁盘情况" :value="`${info.diskUsed}GB/${info.diskTotal}GB`" border />
                <Cell label="内存情况" :value="`${info.swapFree}MB/${info.swapTotal}MB`" border />
                <Cell label="Slot频率" :value="info.slotIntervalCmd" border />
                <Cell label="当前时间" :value="info.timeStamp" border />
                <Cell label="目标机器人数" :value="info.targetRobotNum+''" border />
                <Cell label="投放机器人数" :value="info.putRobotNum+''" border />
                <Cell label="在线机器人数" :value="info.onlineRobotNum+''" border />
                <div slot="footer" class="dialog-footer">
                  <div align="right">
                    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
                      <el-form-item label="操作" prop="num">
                        <el-input
                          v-model="form.num"
                          placeholder="请输入数量"
                          clearable
                          size="small"
                          style="width: 160px"
                        />
                        <el-button type="primary" icon="el-icon-search" size="small" @click="handleAddRobot">添加机器人</el-button>
                        <el-button type="info" icon="el-icon-search" size="small" @click="handleMinusRobot">减少机器人</el-button>
                      </el-form-item>
                    </el-form>
                  </div>
                  <div align="right">
                    <el-button type="success" icon="el-icon-search" size="small" @click="handleOpenSlot"> 开启</el-button>
                    <el-button type="info" icon="el-icon-search" size="small" @click="handleStopSlot"> 关闭</el-button>
                  </div>
                </div>
              </div>
            </el-card>
            <el-card v-else> 😢获取配置机器人地址失败 t_plant_params</el-card>
          </el-col>
          <el-col :span="12" :sm="12" :md="12">
            <el-card v-if="info.code==200" class="box-card" style="width: 100%; margin-top: 5px;">
              <div slot="header" class="clearfix">
                <span> 数据统计 </span>
              </div>
              <div v-loading="loading" class="monitor">
                <el-input
                  type="textarea"
                  autosize
                  :value="formattedStatistics"
                  readonly
                  @focus="$event.target.select()"
                />
              </div>
            </el-card>
          </el-col>
        </el-row>
      </template>
    </BasicLayout>
  </div>
</template>

<script>
import Cell from '@/components/Cell/index'
import { getRobotInfo } from '@/api/gm/srv-robot'
import store from '../../../store'
import { listTPlantParams } from '@/api/plant/plant_params'
import { ENV_TYPE_AUDIT } from '@/api/admin/env'

export default {
  name: 'Monitor',
  components: {
    Cell
  },
  data() {
    return {
      info: {},
      loading: false,
      form: {
      },
      // 表单校验
      rules: { planetId: [{ required: true, message: 'planet不能为空', trigger: 'blur' }],
        num: [{ required: true, message: '字段不能为空', trigger: 'blur' }]
      },
      customColors: [
        { color: '#13ce66', percentage: 20 },
        { color: '#1890ff', percentage: 40 },
        { color: '#e6a23c', percentage: 60 },
        { color: '#1989fa', percentage: 80 },
        { color: '#F56C6C', percentage: 100 }
      ],
      otimer: null,
      isRobotGetFail: 0,
      robotUrl: '',
      urls: {
        query: 'api',
        add: 'gm?ot=add&param=',
        minus: 'gm?ot=minus&param=',
        openSlot: 'gm?ot=slot&param='
      }
    }
  },
  computed: {
    project() {
      return this.$store.getters.project
    },
    formattedStatistics() {
      return JSON.stringify(this.info, null, 2)
    }
  },
  watch: {
    project(newVal) {
      if (newVal && newVal.length > 0) {
        this.handleProjectReady(newVal)
      }
    }
  },
  created() {
    this.watchProjectAndAct()
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll)
    if (this.otimer) {
      clearInterval(this.otimer)
    }
  },
  methods: {
    watchProjectAndAct() {
      if (this.project && this.project.length > 0) {
        this.handleProjectReady(this.project)
      }
    },
    handleProjectReady(project) {
      this.info = {}
      this.robotUrl = ''
      var envType = project[1]
      if (envType === ENV_TYPE_AUDIT) {
        this.$alert('压测程序仅只能是开发环境下使用。', '提示', {
          confirmButtonText: '确定'
        })
      } else {
        var queryParam = {
          paramKey: 'robot_url',
          plantId: project[2]
        }
        this.loading = true
        listTPlantParams(this.addDateRange(queryParam, this.dateRange)).then(response => {
          this.loading = false
          this.tPlantParamsList = response.data.list
          this.robotUrl = this.tPlantParamsList[0]?.paramValue || ''
          this.startQueryInfo()
          this.otimer = setInterval(() => {
            this.getServerInfo()
          }, 5 * 1000)
        }).catch(error => {
          this.loading = false
          console.error('Failed to fetch TPlant Params:', error)
        })
      }
    },
    startQueryInfo() {
      if (this.otimer) {
        clearInterval(this.otimer)
      }

      this.getServerInfo()
    },
    getServerInfo() {
      // var url = this.robotUrl + this.urls.query
      if (this.robotUrl === '') {
        return
      }
      console.log('url', this.robotUrl, this.urls.query)
      var bean = {}
      bean.planetId = store.getters.project[2]
      bean.param = this.urls.query
      bean.reqUrl = this.robotUrl
      bean.doType = 1
      getRobotInfo(bean).then(response => {
        this.info = JSON.parse(response.data)
        console.log(this.info)
      }).catch((err) => {
        this.isRobotGetFail++
        if (this.isRobotGetFail >= 3) {
          clearInterval(this.otimer)
          this.notifyTips()
        }
        console.log(err)
      })
    },
    notifyTips() {
      this.$alert('请启动chaos程序,然后刷新界面。', '压测机器人服务异常', {
        confirmButtonText: '确定',
        callback: action => {
          location.reload()
          this.$message({
            type: 'info',
            message: `刷新页面 ${action}`
          })
        }
      })
    },
    handleAddRobot(row) {
      console.log('RobotAdd', this.form.num)
      var urlNoew = this.urls.add + this.form.num
      this.oprRobot(urlNoew)
    },
    handleMinusRobot(row) {
      console.log('RobotMinus', this.form.nu)
      var urlNoew = this.urls.minus + this.form.num
      this.oprRobot(urlNoew)
    },
    handleOpenSlot(row) {
      var urlNoew = this.urls.openSlot + 0
      this.oprRobot(urlNoew)
    },
    handleStopSlot(row) {
      var urlNoew = this.urls.openSlot + 1
      this.oprRobot(urlNoew)
    },
    oprRobot(param) {
      if (this.form.num !== undefined) {
        this.loading = true
        setTimeout(() => {
          this.loading = false
          this.startQueryInfo()
        }, 5 * 1000)
        var bean = {}
        bean.planetId = store.getters.project[2]
        bean.param = param
        bean.reqUrl = this.robotUrl
        getRobotInfo(bean, 0)
          .catch((err) => {
            console.log(err)
          })
      } else {
        this.msgError('请填写数量')
        return
      }
    }
  }
}

</script>

<style lang="scss" scoped>
.box-card {
  width: 100%;
  margin-top: 5px;
  min-height: 600px; /* 增加最小高度 */
  display: flex;
  flex-direction: column;
}

.monitor {
  flex-grow: 1; /* 使内容区域扩展以填充剩余空间 */
  min-height: 550px; /* 增加最小高度 */
  max-height: 550px; /* 增加最大高度 */
  overflow-y: auto; /* 当内容超过最大高度时显示滚动条 */
  .monitor-header {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .monitor-progress {
    padding-top: 15px;
  }
}
</style>

