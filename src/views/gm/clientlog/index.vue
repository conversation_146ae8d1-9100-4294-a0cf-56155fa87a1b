<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card-done">
        <div slot="header" size="small" class="clearfix">
          <span>日志开启</span>
        </div>
        <el-form ref="queryForm" :model="queryParams" :rules="rules" :inline="true" label-width="68px">
          <el-form-item label="用户ID" prop="uid"><el-input
            v-model="queryParams.uid"
            placeholder="请输入用户ID"
            clearable
            size="small"
          />
          </el-form-item>
          <el-form-item label="时间" prop="timeStamp">
            <el-date-picker
              ref="datesRef"
              v-model="searchObj.dateArr"
              type="dates"
              :editable="false"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              placeholder="选择一个或多个日期"
            />
          </el-form-item>
          <el-form-item label="备注" prop="remark"><el-input
            v-model="queryParams.remark"
            placeholder="请输入备注"
            clearable
            size="small"
          />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-refresh" size="mini" @click="submitForm">开启日志</el-button>
          </el-form-item>
        </el-form>
      </el-card>
      <!-- 拉取S3日志 -->
      <el-card class="box-card-log">
        <div slot="header" size="small" class="clearfix">
          <span>日志查询(从S3下载)</span>
        </div>
        <el-form ref="queryLogForm" :model="queryLogParams" :rules="rules" :inline="true" label-width="68px">
          <el-form-item label="用户ID" prop="uid"><el-input
            v-model="queryLogParams.uid"
            placeholder="请输入用户ID"
            clearable
            size="small"
            @keyup.enter.native="handleLogQuery"
          />
          </el-form-item>
          <el-form-item label="日期" prop="dayTime">
            <el-date-picker
              v-model="queryLogParams.dayTime"
              size="small"
              type="date"
              placeholder="选择日期"
              :picker-options="pickerOptions0"
              align="right"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-refresh" size="mini" @click="handleLogQuery">查询</el-button>
            <el-button
              v-permisaction="['admin:sysOperLog:export']"
              type="warning"
              icon="el-icon-download"
              size="mini"
              @click="downLoadBatch"
            >批量下载</el-button>
          </el-form-item>
        </el-form>
        <el-table v-loading="loading1" :data="tClientS3LogList" @selection-change="handleSelectionChange">
          <!-- <el-table-column type="selection" width="55" align="center" /> -->
          <el-table-column align="center" width="80">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column
            label="文件"
            align="left"
            prop="key"
            width="500"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="大小"
            align="left"
            prop="size"
            width="180"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{ changeByte(scope.row.size) }}</span>
            </template>
          </el-table-column><el-table-column
            label="最后修改时间"
            align="left"
            prop="timeStamp"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.timeStamp) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要下载吗?"
                confirm-button-text="下载"
                @onConfirm="handleExport(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['gm:tClientLogList:edit']"
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                >下载
                </el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="totalRemote>0"
          :total="totalRemote"
          :page.sync="queryLogParams.pageIndex"
          :limit.sync="queryLogParams.pageSize"
          @pagination="handleLogQuery"
        />
      </el-card>
      <!-- 操作记录 -->
      <el-card class="box-card">
        <div slot="header" size="small" class="clearfix">
          <span>操作记录</span>
        </div>
        <el-form ref="queryRecordForm" :model="queryListParams" :inline="true" label-width="68px">
          <el-form-item label="操作者" prop="createName"><el-input
            v-model="queryListParams.createName"
            placeholder="请输入操作者"
            clearable
            size="mini"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="备注" prop="remark"><el-input
            v-model="queryListParams.remark"
            placeholder="请输入备注"
            clearable
            size="mini"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item>
            <template>
              <el-checkbox v-model="queryListParams.logStatus" :true-label="1" :false-label="undefined">仅查未关闭</el-checkbox>
            </template>
          </el-form-item>
          <el-form-item label="时间戳" prop="timeStamp">
            <el-date-picker
              v-model="dateRange"
              size="mini"
              type="datetimerange"
              :picker-options="pickerOptions"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              align="right"
              value-format="yyyy-MM-dd HH:mm:ss"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table v-loading="loading2" :data="tClientLogListList" @selection-change="handleSelectionChange">
          <el-table-column align="center" width="80">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column
            label="用户ID"
            align="left"
            prop="uid"
            width="100"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="操作者"
            align="left"
            prop="createName"
            width="120"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="状态"
            align="center"
            prop="logStatus"
            width="120"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <el-tag v-if="scope.row.logStatus==1" type="success"> 开启 </el-tag>
              <el-tag v-if="scope.row.logStatus==2" type="info"> 关闭 </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="备注"
            align="left"
            prop="remark"
            width="180"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="设置时间"
            align="left"
            prop="timeStamp"
            width="320"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="时间戳"
            align="left"
            prop="createdAt"
            width="160"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createdAt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                v-permisaction="['gm:tClientLogList:query']"
                size="mini"
                type="text"
                icon="el-icon-circle-check"
                @click="handleAddQuery(scope.row)"
              >去检索</el-button>
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要关闭吗?"
                confirm-button-text="关闭"
                @onConfirm="handleUpdate(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['gm:tClientLogList:edit']"
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                >关闭
                </el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryListParams.pageIndex"
          :limit.sync="queryListParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { addTClientLogList, delTClientLogList, updateTClientLogList, listTClientLogList, listS3LogList } from '@/api/gm/srv-client-log'
import store from '../../../store'
import JSZip from 'jszip'
import FileSaver from 'file-saver'
import axios from 'axios'

const getFile = url => {
  return new Promise((resolve, reject) => {
    axios({
      method: 'get',
      url,
      responseType: 'blob'
    })
      .then(data => {
        resolve(data.data)
      })
      .catch(error => {
        reject(error.toString())
      })
  })
}

export default {
  name: 'TClientLogList',
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading1: false,
      loading2: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      tClientLogListList: [],
      tClientS3LogList: [],

      // 关系表类型

      // 添加日志策略参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        uid: undefined,
        timeStamp: undefined
      },
      searchObj: {}, // 条件封装对象
      // 查询参数
      queryLogParams: {
        pageIndex: 1,
        pageSize: 10,
        uid: undefined,
        dayTime: undefined
      },
      queryListParams: {
        pageIndex: 1,
        pageSize: 10,
        uid: undefined,
        dayTime: undefined,
        createName: undefined,
        remark: undefined,
        logStatus: undefined,
        planetId: undefined
      },
      // 总远程日志条数
      totalRemote: 0,
      // 日期范围
      dateRange: [],
      pickerOptions0: {
        // disabledDate(time) {
        //   return time.getTime() < Date.now() - 8.64e7
        // }
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      logStatus: undefined,
      // 表单参数
      form: {
      },
      // 表单校验
      rules: { uid: [{ required: true, message: '用户ID不能为空', trigger: 'blur' }],
        remark: [{ required: true, message: '备注不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.dateArr = []
    // this.getList()
  },
  mounted: function() {
    this.$nextTick(function() {
      this.dateArr = []
      this.$refs.datesRef.showPicker()
      this.$refs.datesRef.hidePicker()
    })
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading2 = true
      const plantID = store.getters.project[2]
      this.queryListParams.planetId = plantID
      console.log(this.queryListParams)

      listTClientLogList(this.addDateRange(this.queryListParams, this.dateRange)).then(response => {
        this.tClientLogListList = response.data.list
        this.total = response.data.count
        this.loading2 = false
      }
      )
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {

        id: undefined,
        planetId: undefined,
        uid: undefined,
        logStatus: undefined,
        remark: undefined,
        timeStamp: undefined
      }
      this.resetForm('form')
    },
    getImgList: function() {
      this.form[this.fileIndex] = this.$refs['fileChoose'].resultList[0].fullUrl
    },
    fileClose: function() {
      this.fileOpen = false
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryListParams.pageIndex = 1
      this.getList()
    },
    // 添加搜索条件
    handleAddQuery(data) {
      console.log('handleRecord data:', data.uid)
      this.queryLogParams.uid = data.uid
      window.scrollTo(0, 0)
      this.handleLogQuery()
    },
    // 日志列表
    /** 搜索按钮操作 */
    handleLogQuery() {
      this.$refs['queryLogForm'].validate(valid => {
        if (valid) {
          // if (typeof this.queryLogParams.dayTime === 'undefined') {
          //   this.msgError('请选择单个，或者多个日期！')
          //   return
          // }
          this.loading1 = true
          console.log('handleLogQuery...', this.queryLogParams)
          this.queryParams.pageIndex = 1
          listS3LogList(this.queryLogParams).then(response => {
            this.tClientS3LogList = response.data.list
            this.totalRemote = response.data.count
            console.log(response.data.list)
            this.loading1 = false
          }
          )
        }
      })
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryRecordForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加TClientLogList'
      this.isEdit = false
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      updateTClientLogList(row).then(response => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      })
    },
    submitForm: function() {
      this.$refs['queryForm'].validate(valid => {
        if (valid) {
          if (typeof this.searchObj.dateArr === 'undefined') {
            this.msgError('请选择单个，或者多个日期！')
            return
          }
          console.log(this.searchObj.dateArr)

          this.$confirm('确定对UID(' + this.queryParams.uid + ')客户端日志策略操作？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            var printStr = this.searchObj.dateArr ? this.searchObj.dateArr.join() : ''
            console.log(printStr)
            console.log(this.queryParams)
            this.queryParams.logStatus = 1
            this.queryParams.timeStamp = printStr
            addTClientLogList(this.queryParams).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          })
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var Ids = (row.id && [row.id]) || this.ids

      this.$confirm('是否确认删除编号为"' + Ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delTClientLogList({ 'ids': Ids })
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function() {
      })
    },
    handleExport(row) {
      console.log(row)
      var url = row.url
      console.log(url)
      // const link = document.createElement('a')
      // fetch(url, {
      //   mode: 'no-cors'
      // })
      //   .then((res) => res.blob())
      //   .then((blob) => {
      //     // 将链接地址字符内容转变成blob地址
      //     link.href = URL.createObjectURL(blob)
      //     link.download = row.fileName
      //     document.body.appendChild(link)
      //     link.click()
      //     document.body.removeChild(link)
      //   })
      const a = document.createElement('a')
      a.href = url
      a.download = row.fileName // 下载后文件名
      a.style.display = 'none'
      document.body.appendChild(a)
      a.click() // 点击下载
      document.body.removeChild(a) // 下载完成移除元素
    },
    // 文件下载
    downLoadBatch(row) {
      this.loading1 = true
      var queryBatch = this.queryLogParams
      queryBatch.batchFlg = 1
      var zipFileName = 'client_log_' + queryBatch.uid + '.zip'
      listS3LogList(this.queryLogParams).then(response => {
        var allList = response.data.list
        var fileList = []

        console.log('downLoadBatch', allList)

        for (let index = 0; index < allList.length; index++) {
          const element = allList[index]
          fileList.push(element.url)
        }

        const data = fileList // 需要下载打包的路径, 可以是本地相对路径, 也可以是跨域的全路径
        const zip = new JSZip()
        const cache = {}
        const promises = []
        data.forEach(item => {
          const promise = getFile(item).then(data => {
            // 下载文件, 并存成ArrayBuffer对象
            const arr_name = item.split('/')
            const file_name = arr_name[arr_name.length - 1] // 获取文件名
            zip.file(file_name, data, { binary: true }) // 逐个添加文件
            cache[file_name] = data
          })
          promises.push(promise)
        })
        Promise.all(promises).then(() => {
          zip.generateAsync({ type: 'blob' }).then(content => {
            // 生成二进制流
            FileSaver.saveAs(content, zipFileName) // 利用file-saver保存文件  自定义文件名
            this.loading1 = false
          })
        })
      }
      )
    }
  }
}
</script>
