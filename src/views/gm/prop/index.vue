<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true">
          <el-form-item label="启用状态：" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="全部"
              clearable
              size="small"
              style="width: 160px"
            >
              <el-option
                v-for="dict in statusOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="GM命令：" prop="gmCmd">
            <el-input
              v-model="queryParams.gmCmd"
              placeholder="请输入CMD"
              clearable
              size="small"
              style="width: 160px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="分类：" prop="gmType">
            <el-input
              v-model="queryParams.gmType"
              placeholder="请输入分类"
              clearable
              size="small"
              style="width: 160px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-table
          v-loading="loading"
          :data="gmList"
          border
        >
          <el-table-column label="序号" type="index" width="80" align="center" />
          <el-table-column label="名称" align="left" sortable="custom" prop="gmName" width="500" :show-overflow-tooltip="true" />
          <el-table-column label="分类" align="center" sortable="custom" prop="gmType" width="120" :show-overflow-tooltip="true" />
          <el-table-column label="启用状态" sortable="custom" width="110" align="center">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.status"
                active-value="2"
                inactive-value="1"
                disabled="disabled"
                @change="handleStatusChange(scope.row)"
              />
            </template>
          </el-table-column>
          <!-- <el-table-column label="创建时间" sortable="custom" prop="createdAt" width="160">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createdAt) }}</span>
            </template>
          </el-table-column> -->
          <el-table-column label="CMD" align="center" sortable="custom" prop="gmCmd" width="120" :show-overflow-tooltip="true" />
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
              >操作</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-circle-check"
                @click="handleRecord(scope.row)"
              >操作记录</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

        <!-- 修改对话框 -->
        <el-dialog v-if="open" v-loading="loading" :title="title" :visible.sync="open" width="700px" :before-close="handleClose">
          <form-create v-model="$data.$f" :rules="rules" :rule="rule" :option="$data.optionForm" />
        </el-dialog>
        <el-dialog title="GM操作记录" :visible.sync="recordOpen" width="1500px">
          <el-dialog
            width="50%"
            title="GM命令操作记录"
            :visible.sync="innerVisible"
            append-to-body
          >
            <el-form ref="logForm" :model="logForm" label-width="80px">
              <el-row>
                <el-col :span="6" align="left">
                  <el-form-item label="玩家ID：">{{ logForm.playId }}</el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12" align="left">
                  <el-form-item label="时间戳：">{{ parseTime(logForm.timeStamp) }}</el-form-item>
                </el-col>
                <el-col :span="12" align="left">
                  <el-form-item label="CMD：">{{ logForm.gmCmd }}</el-form-item>
                </el-col>
                <el-col :span="12" align="left">
                  <el-form-item label="环境：">{{ logForm.gmEnv }}</el-form-item>
                </el-col>
                <el-col :span="12" align="left">
                  <el-form-item label="备注：">{{ logForm.remark }}</el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="请求参数" prop="requestJson">
                <pre> {{ jsonView(logForm.requestJson) }}</pre>
              </el-form-item>
              <el-form-item label="返回结果" prop="rspJson" label-width="100px">
                <pre> {{ jsonView(logForm.rspJson) }}</pre>
              </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
              <el-button @click="logCancel">取 消</el-button>
            </div>
          </el-dialog>
          <el-card class="box-card">
            <el-table v-loading="loading" :data="logGmList">
              <el-table-column width="55" align="center" /><el-table-column
                label="玩家ID"
                align="center"
                width="120"
                prop="playId"
                :show-overflow-tooltip="true"
              /><el-table-column
                label="时间戳"
                align="center"
                width="160"
                prop="timeStamp"
                :show-overflow-tooltip="true"
              >
                <template slot-scope="scope">
                  <span>{{ parseTime(scope.row.timeStamp) }}</span>
                </template>
              </el-table-column><el-table-column
                label="CMD"
                align="center"
                width="120"
                prop="gmCmd"
                :show-overflow-tooltip="true"
              /><el-table-column
                label="环境"
                align="center"
                width="120"
                prop="gmEnv"
                :show-overflow-tooltip="true"
              /><el-table-column
                label="操作人"
                align="center"
                width="180"
                prop="gmOperator"
                :show-overflow-tooltip="true"
              /><el-table-column
                label="备注"
                align="center"
                width="180"
                prop="remark"
                :show-overflow-tooltip="true"
              />
              <el-table-column label="操作" align="left" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <el-button
                    slot="reference"
                    v-permisaction="['gm:logGm:edit']"
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    @click="handleLogUpdate(scope.row)"
                  >查看
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="logTotal>0"
              :total="logTotal"
              :page.sync="logQueryParams.pageIndex"
              :limit.sync="logQueryParams.pageSize"
              @pagination="getLogList"
            />
          </el-card>
        </el-dialog>
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { gmList, getGmInfo, propTypeList, handleGmCmd } from '@/api/gm/srv-gm'
import formCreate from '@form-create/element-ui'
import 'default-passive-events'
import { listLogGm, getLogGm } from '@/api/gm/srv-gm-log'

export default {
  name: 'Gm',
  components: {
    formCreate: formCreate.$form()
  },
  data() {
    const self = this
    return {
      innerVisible: false,
      recordOpen: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      logTotal: 0,
      // 角色表格数据
      gmList: [],
      menuIdsChecked: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      isEdit: false,
      // 日期范围
      dateRange: [],
      // 状态数据字典
      statusOptions: [],
      // ],
      // Prop列表
      propOptions: [],
      // 数据范围选项
      dataScopeOptions: [
        {
          value: '1',
          label: '全部数据权限'
        },
        {
          value: '2',
          label: '自定数据权限'
        },
        {
          value: '3',
          label: '本部门数据权限'
        },
        {
          value: '4',
          label: '本部门及以下数据权限'
        },
        {
          value: '5',
          label: '仅本人数据权限'
        }
      ],
      menuOptionsAlert: '加载中，请稍后',
      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        gmType: undefined,
        gmCmd: undefined,
        status: undefined
      },
      logGmList: [],
      logQueryParams: {
        pageIndex: 1,
        pageSize: 10,
        playId: undefined,
        timeStamp: undefined,
        gmEnv: undefined,
        gmCmd: undefined,
        gmOperator: undefined
      },
      logForm: {
      },
      // 表单参数
      form: {
        sysMenu: []
      },
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      $f: {},
      rule: [],
      optionForm: {
        onSubmit: function(formData) {
          console.log(formData)
          // alert(JSON.stringify(formData))
          self.submitForm(formData)
        }
      },
      // 表单校验
      rules: {
        NotEmpty: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
    this.getPropTreeselect()
    this.getDicts('sys_normal_disable').then(response => {
      this.statusOptions = response.data
    })
  },
  methods: {
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done()
        })
        .catch(_ => {})
    },
    /** 查询GM列表 */
    getList() {
      this.loading = true
      gmList(this.addDateRange(this.queryParams, this.dateRange)).then(
        response => {
          this.gmList = response.data.list
          this.total = response.data.count
          this.loading = false
        }
      )
    },
    /** 查询Prop */
    getPropTreeselect() {
      this.loading = true
      propTypeList().then(
        response => {
          console.log(response)
          this.propOptions = response.data

          this.loading = false
        }
      )
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 取消按钮
    logCancel() {
      this.innerVisible = false
      this.reset()
    },
    /** Json格式化 */
    jsonView(str) {
      if (str == null || str.length === 0) {
        return ''
      } else {
        return JSON.parse(str)
      }
    },
    // 取消按钮
    cancelDataScope() {
      this.openDataScope = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.menuOptions = this.gmList
      if (this.$refs.menuTree !== undefined) {
        this.$refs.menuTree.setCheckedKeys([])
      }
      this.form = {
        gmId: undefined,
        gmName: undefined,
        gmKey: undefined,
        gmSort: 0,
        status: '2',
        menuIds: [],
        deptIds: [],
        sysMenu: [],
        gmOperator: '',
        gmParamJson: '',
        remark: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    handleSortChang(column, prop, order) {
      prop = column.prop
      order = column.order
      if (order === 'descending') {
        this.queryParams[prop + 'Order'] = 'desc'
      } else if (order === 'ascending') {
        this.queryParams[prop + 'Order'] = 'asc'
      } else {
        this.queryParams[prop + 'Order'] = undefined
      }
      this.getList()
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.menuIdsChecked = []
      this.reset()
      const gmId = row.gmId || this.ids
      getGmInfo(gmId).then(response => {
        this.form = response.data
        this.form.gmOperator = this.$store.getters.name
        this.menuIdsChecked = response.data.gmId
        this.title = response.data.gmType
        this.isEdit = true
        this.open = true
        // this.getPropTreeselect()

        console.log('row Json:', response.data.gmParam)
        this.rule = []
        this.rule = JSON.parse(response.data.gmParam)
        var eleOptions = []
        for (let index = 0; index < this.propOptions.length; index++) {
          const element = this.propOptions[index]
          var opt = {}
          opt.value = element.propId
          opt.label = element.propName
          eleOptions.push(opt)
        }

        for (let index2 = 0; index2 < this.rule.length; index2++) {
          var element = this.rule[index2]
          if (element.$required === true) {
            console.log('$required', element.$required)
            this.rule[index2].validate = this.rules.NotEmpty
          }
          var fd = element.field
          if (fd === 'PropType') {
            console.log(element)
            this.rule[index2].options = eleOptions
            this.rule[index2].validate = this.rules.NotEmpty
          }
          if (fd === 'gmName') {
            this.rule[index2].value = response.data.gmName
          }
          if (fd === 'gmCmd') {
            this.rule[index2].value = response.data.gmCmd
          }
          if (fd === 'gmUid') {
            this.rule[index2].validate = this.rules.NotEmpty
          }
          if (fd === 'gmOperator') {
            this.rule[index2].value = this.form.gmOperator
          }
          if (fd === 'Value') {
            this.rule[index2].validate = this.rules.NotEmpty
          }
          if (fd === 'reason') {
            this.rule[index2].validate = this.rules.NotEmpty
          }
        }
      })
    },
    /** 修改按钮操作 */
    handleRecord(data) {
      console.log('handleRecord data:', data)
      this.logQueryParams.gmCmd = data.gmCmd
      this.getLogList()
      this.recordOpen = true
    },
    /** 查询参数列表 */
    getLogList() {
      this.loading = true
      listLogGm(this.addDateRange(this.logQueryParams, this.dateRange)).then(response => {
        this.logGmList = response.data.list
        this.logTotal = response.data.count
        this.loading = false
      }
      )
    },
    /** 修改按钮操作 */
    handleLogUpdate(row) {
      const logId =
        row.id || this.ids
      getLogGm(logId).then(response => {
        this.innerVisible = true
        this.logForm = response.data
        this.isEdit = true
      })
    },
    /** 提交按钮 */
    submitForm: function(formData) {
      // alert(JSON.stringify(formData))
      this.loading = true
      console.log('发送请求formData：', formData)
      // 字符串转string
      Object.keys(formData).forEach(key => {
        if (typeof formData[key] === 'string' && (formData[key].startsWith('[') && formData[key].endsWith(']'))) {
          console.log('formData[key]', key, formData[key])
          formData[key] = JSON.parse(formData[key])
        }
      })
      const resetState = () => {
        setTimeout(() => {
          this.loading = false
        }, 1000)
        // this.open = false
      }
      handleGmCmd(formData).then(
        response => {
          console.log(response)
          this.msgSuccess(response.msg)
          resetState()
        }
      ).catch(error => {
        console.error(error)
        resetState()
      })
    }
  }
}

</script>
