
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="操作类型" prop="rfmType"><el-select
            v-model="queryParams.rfmType"
            placeholder="RFM管理操作类型"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in rfmTypeOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          </el-form-item>
          <el-form-item label="备注" prop="remark"><el-input
            v-model="queryParams.remark"
            placeholder="请输入备注"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <el-button
              v-permisaction="['gm:tRfmMaster:add']"
              type="primary"
              icon="el-icon-setting"
              size="mini"
              plain
              @click="handleAdd"
            >操作
            </el-button>
          </el-form-item>
        </el-form>

        <el-table v-loading="loading" :data="tRfmMasterList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" /><el-table-column
            label="操作类型"
            align="left"
            prop="rfmType"
            :formatter="rfmTypeFormat"
            width="160"
          >
            <template slot-scope="scope">
              {{ rfmTypeFormat(scope.row) }}
            </template>
          </el-table-column><el-table-column
            label="执行结果"
            align="left"
            prop="rpcRsp"
            width="860"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                slot="reference"
                v-permisaction="['gm:tRfmMaster:edit']"
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
              >查看
              </el-button>
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要删除吗?"
                confirm-button-text="删除"
                @onConfirm="handleDelete(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['gm:tRfmMaster:remove']"
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                >删除
                </el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="700px">
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">
            <el-form-item label="操作类型" prop="rfmType">
              <el-select
                v-model="form.rfmType"
                placeholder="请选择"
                :disabled="isEdit"
              >
                <el-option
                  v-for="dict in rfmTypeOptions"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
              <el-tooltip placement="top">
                <div slot="content">
                  <pre>
    // RFM分组<br>
    enum RFM_GROUP {<br>
        RFM_GROUP_INIT                    = 0;   //<br>
        RFM_GROUP_NOT_PAY                 = 1;   //    [RFM用户分组]非付费用户<br>
        RFM_GROUP_PAY_PASS                = 2;   //    [RFM用户分组]近期未付费用户<br>
        RFM_GROUP_ACTIVE_MORE_PAY_BR      = 3;   //    [RFM用户分组]活跃多次付费大R<br>
        RFM_GROUP_SILENCE_MORE_PAY_BR     = 4;   //    [RFM用户分组]沉默多次付费大R<br>
        RFM_GROUP_ACTIVE_ONE_PAY_BR       = 5;   //    [RFM用户分组]活跃一次付费大R<br>
        RFM_GROUP_SILENCE_ONE_PAY_BR      = 6;   //    [RFM用户分组]沉默一次付费大R<br>
        RFM_GROUP_ACTIVE_MORE_PAY_MR      = 7;   //    [RFM用户分组]活跃多次付费中R<br>
        RFM_GROUP_SILENCE_MORE_PAY_MR     = 8;   //    [RFM用户分组]沉默多次付费中R<br>
        RFM_GROUP_ACTIVE_ONE_PAY_MR       = 9;   //    [RFM用户分组]活跃一次付费中R<br>
        RFM_GROUP_SILENCE_ONE_PAY_MR      = 10;  //    [RFM用户分组]沉默一次付费中R<br>
        RFM_GROUP_ACTIVE_MORE_PAY_LR      = 11;  //    [RFM用户分组]活跃多次付费小R<br>
        RFM_GROUP_SILENCE_MORE_PAY_LR     = 12;  //    [RFM用户分组]沉默多次付费小R<br>
        RFM_GROUP_ACTIVE_ONE_PAY_LR       = 13;  //    [RFM用户分组]活跃一次付费小R<br>
        RFM_GROUP_SILENCE_ONE_PAY_LR      = 14;  //    [RFM用户分组]沉默一次付费小R<br>
    }
                </pre>
                </div>
                <el-button>查看RFM_GROUP定义</el-button>
              </el-tooltip>
            </el-form-item>
            <el-form-item label="玩家ID" prop="rpcReq">
              <el-input
                v-model="form.rpcReq"
                :v-show="true"
                :disabled="isEdit"
                placeholder="rpc请求Json"
              />
            </el-form-item>
            <el-form-item label="操作结果" prop="rpcRsp">
              <el-input
                v-model="form.rpcRsp"
                :disabled="isEdit"
                type="textarea"
                rows="12"
                placeholder="rpc返回Json"
              />
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="form.remark"
                :disabled="isEdit"
                placeholder="备注"
              />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" :disabled="isEdit" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { addTRfmMaster, delTRfmMaster, getTRfmMaster, listTRfmMaster, updateTRfmMaster } from '@/api/gm/srv-gm-rfm'
import store from '../../../store'

export default {
  name: 'TRfmMaster',
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      tRfmMasterList: [],
      rfmTypeOptions: [],
      // 关系表类型

      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        rfmType: undefined,
        remark: undefined

      },
      // 表单参数
      form: {
      },
      // 表单校验
      rules: { rfmType: [{ required: true, message: '操作类型不能为空', trigger: 'blur' }],
        remark: [{ required: true, message: '备注不能为空', trigger: 'blur' }],
        rpcReq: [{ required: true, message: '请求参数不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
    this.getDicts('rfm_operate_type').then(response => {
      this.rfmTypeOptions = response.data
    })
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      this.queryParams.planetId = store.getters.project[2]
      listTRfmMaster(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.tRfmMasterList = response.data.list
        this.total = response.data.count
        this.loading = false
      }
      )
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {

        id: undefined,
        planetId: undefined,
        rfmType: undefined,
        rpcReq: undefined,
        rpcRsp: undefined,
        remark: undefined
      }
      this.resetForm('form')
    },
    getImgList: function() {
      this.form[this.fileIndex] = this.$refs['fileChoose'].resultList[0].fullUrl
    },
    fileClose: function() {
      this.fileOpen = false
    },
    rfmTypeFormat(row) {
      return this.selectDictLabel(this.rfmTypeOptions, row.rfmType)
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = 'RFM操作'
      this.isEdit = false
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id =
              row.id || this.ids
      getTRfmMaster(id).then(response => {
        this.form = response.data
        var paraJsonStr = this.jsonView(response.data.rpcRsp)
        this.form.rpcRsp = JSON.stringify(paraJsonStr, null, 4)
        this.form.rfmType = this.form.rfmType + ''
        this.open = true
        this.title = 'RFM操作结果'
        this.isEdit = true
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      console.log('form', this.form)
      this.$refs['form'].validate(valid => {
        this.form.planetId = store.getters.project[2]
        this.form.rfmType = Number(this.form.rfmType)

        if (valid) {
          if (this.form.id !== undefined) {
            updateTRfmMaster(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          } else {
            addTRfmMaster(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          }
        }
      })
    },
    /** Json格式化 */
    jsonView(str) {
      if (str == null || str.length === 0) {
        return ''
      } else {
        return JSON.parse(str)
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var Ids = (row.id && [row.id]) || this.ids

      this.$confirm('是否确认删除编号为"' + Ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delTRfmMaster({ 'ids': Ids })
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function() {
      })
    }
  }
}
</script>
