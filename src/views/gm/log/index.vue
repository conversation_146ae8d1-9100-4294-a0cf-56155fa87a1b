
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <!-- <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="玩家ID" prop="playId"><el-input
            v-model="queryParams.playId"
            placeholder="请输入玩家ID"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="时间戳" prop="timeStamp"><el-input
            v-model="queryParams.timeStamp"
            placeholder="请输入时间戳"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="环境" prop="gmEnv"><el-input
            v-model="queryParams.gmEnv"
            placeholder="请输入1:dev 2:test 3:pre 4:product"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form> -->

        <el-table v-loading="loading" :data="logGmList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" /><el-table-column
            label="玩家ID"
            align="center"
            width="120"
            prop="playId"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="时间戳"
            align="center"
            width="160"
            prop="timeStamp"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.timeStamp) }}</span>
            </template>
          </el-table-column><el-table-column
            label="CMD"
            align="center"
            width="120"
            prop="gmCmd"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="环境"
            align="center"
            width="120"
            prop="gmEnv"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="操作人"
            align="center"
            width="180"
            prop="gmOperator"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="备注"
            align="center"
            width="180"
            prop="remark"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="操作" align="left" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                slot="reference"
                v-permisaction="['gm:logGm:edit']"
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
              >查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="700px">
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">
            <el-row>
              <el-col :span="6" align="left">
                <el-form-item label="玩家ID：">{{ form.playId }}</el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12" align="left">
                <el-form-item label="时间戳：">{{ parseTime(form.timeStamp) }}</el-form-item>
              </el-col>
              <el-col :span="12" align="left">
                <el-form-item label="CMD：">{{ form.gmCmd }}</el-form-item>
              </el-col>
              <el-col :span="12" align="left">
                <el-form-item label="环境：">{{ form.gmEnv }}</el-form-item>
              </el-col>
              <el-col :span="12" align="left">
                <el-form-item label="备注：">{{ form.remark }}</el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="请求参数" prop="requestJson">
              <pre> {{ jsonView(form.requestJson) }}</pre>
            </el-form-item>
            <el-form-item label="返回结果" prop="rspJson">
              <pre> {{ jsonView(form.rspJson) }}</pre>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <!-- <el-button type="primary" @click="submitForm">确 定</el-button> -->
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { addLogGm, delLogGm, getLogGm, listLogGm, updateLogGm } from '@/api/gm/srv-gm-log'

export default {
  name: 'GmLogView',
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      logGmList: [],

      // 关系表类型

      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        playId: undefined,
        timeStamp: undefined,
        gmEnv: undefined,
        gmCmd: undefined,
        gmOperator: undefined
      },
      // 表单参数
      form: {
      },
      // 表单校验
      rules: { playId: [{ required: true, message: '玩家ID不能为空', trigger: 'blur' }],
        timeStamp: [{ required: true, message: '时间戳不能为空', trigger: 'blur' }],
        gmEnv: [{ required: true, message: '1:dev 2:test 3:pre 4:product不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    const datas = this.$route.params && this.$route.params.data
    console.log('页面数据:', datas)
    this.queryParams.gmCmd = datas.gmCmd
    this.getList()
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      listLogGm(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.logGmList = response.data.list
        this.total = response.data.count
        this.loading = false
      }
      )
    },
    /** Json格式化 */
    jsonView(str) {
      if (str == null || str.length === 0) {
        return ''
      } else {
        return JSON.parse(str)
      }
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {

        id: undefined,
        playId: undefined,
        timeStamp: undefined,
        gmCmd: undefined,
        gmType: undefined,
        gmEnv: undefined,
        requestJson: undefined,
        remark: undefined,
        rspJson: undefined
      }
      this.resetForm('form')
    },
    getImgList: function() {
      this.form[this.fileIndex] = this.$refs['fileChoose'].resultList[0].fullUrl
    },
    fileClose: function() {
      this.fileOpen = false
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加LogGm'
      this.isEdit = false
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id =
                row.id || this.ids
      getLogGm(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = 'GM命令操作记录'
        this.isEdit = true
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id !== undefined) {
            updateLogGm(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          } else {
            addLogGm(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var Ids = (row.id && [row.id]) || this.ids

      this.$confirm('是否确认删除编号为"' + Ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delLogGm({ 'ids': Ids })
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function() {
      })
    }
  }
}
</script>
