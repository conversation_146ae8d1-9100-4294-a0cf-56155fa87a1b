<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :rules="rules" :inline="true" label-width="68px">
          <el-form-item label="玩家Id" prop="player_id">
            <el-input
              v-model="queryParams.player_id"
              placeholder="请输入玩家Id"
              clearable
              size="small"
              style="width: 200px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table v-loading="loading" :data="playerList" @selection-change="handleSelectionChange">
          <el-table-column label="玩家Id" align="center" prop="brief_user_info.player_id" :show-overflow-tooltip="true" />
          <el-table-column label="玩家名称" align="center" prop="brief_user_info.name" :show-overflow-tooltip="true" />
          <el-table-column label="等级" align="center" prop="brief_user_info.lev" :show-overflow-tooltip="true" />
          <el-table-column label="平台" align="center" prop="platform" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <span>  {{ getPlatformName(scope.row.platform) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="APP 版本" align="center" prop="app_version" :show-overflow-tooltip="true" />
          <el-table-column label="注册时间" align="center" prop="register_time" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.register_time) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                v-permisaction="['payment:list:query']"
                size="mini"
                type="text"
                icon="el-icon-view"
                @click="getDetail(scope)"
              >详情信息
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
        <el-dialog title="用户详情" :visible.sync="open" width="960px">
          <el-form ref="form" :model="form" label-width="100px" size="mini" style="margin-top: -35px">
            <el-row>
              <el-col :span="24"><h4 style="height: 5px;">账号信息</h4></el-col>
            </el-row>
            <el-row style="margin-right: 12px; margin-top: 10px;">
              <el-col :span="21">
                <el-col :span="8" align="left">用户ID：{{ form.ProfileInfo.UID }}</el-col>
                <el-col :span="8" align="left">昵称：{{ form.ProfileInfo.Name }}</el-col>
                <el-col :span="8" align="left">账户类型: {{
                  (function(acctype) {
                    if (acctype == 1) {
                      return '游客'
                    }
                    if (acctype == 2) {
                      return 'FB'
                    }
                    if (acctype == 3) {
                      return 'Apple'
                    }
                  })(form.ProfileInfo.AccType)
                }}
                </el-col>
                <el-col :span="8" align="left" style="margin-top: 5px;">国家：{{ form.ProfileInfo.Country }}</el-col>
                <el-col :span="8" align="left" style="margin-top: 5px;">
                  注册时间：{{ new Date(form.ProfileInfo.CreateTime * 1000).toLocaleString() }}
                </el-col>
                <el-col :span="8" align="left" style="margin-top: 5px;">
                  上次登录时间：{{ new Date(form.ProfileInfo.LastLoginTime * 1000).toLocaleString() }}
                </el-col>
                <el-col :span="8" align="left" style="margin-top: 5px;">OpenID：{{ form.ProfileInfo.OpenID }}</el-col>
                <el-col :span="8" align="left" style="margin-top: 5px;">邮箱：{{ form.ProfileInfo.Mail }}</el-col>
                <el-col :span="8" align="left" style="margin-top: 5px;">是否在线：{{
                  (function(IsOnline) {
                    if (IsOnline) {
                      return '是'
                    }
                    return '否'
                  })(form.ProfileInfo.IsOnline)
                }}
                </el-col>
              </el-col>
              <el-col :span="3">
                <img :src="form.ProfileInfo.avatarURL" width="90px" height="90px" alt="" srcset="">
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24"><h4 style="height: 5px;">设备信息</h4></el-col>
            </el-row>
            <el-row style="margin-right: 12px; margin-top: 10px;">
              <el-col :span="8" align="left">设备型号: {{ form.DeviceInfo.DeviceModel }}</el-col>
              <el-col :span="8" align="left">操作系统：{{ form.DeviceInfo.Os }}</el-col>
              <el-col :span="8" align="left">APP版本: {{ form.DeviceInfo.AppVersion }}</el-col>
              <el-col :span="8" align="left" style="margin-top: 5px;">AdjustID: {{ form.ProfileInfo.AdjustID }}</el-col>
            </el-row>
            <el-row>
              <el-col :span="24"><h4 style="height: 5px;">建筑信息</h4></el-col>
            </el-row>
            <el-row style="margin-right: 12px; margin-top: 10px;">
              <el-col :span="8" align="left">关卡ID: {{ form.EpisodeInfo.EpisodeID }}</el-col>
              <el-col :span="8" align="left">部件1: {{ getRenovateStatus(getRenovateInfo(1)) }}</el-col>
              <el-col :span="8" align="left">部件2: {{ getRenovateStatus(getRenovateInfo(2)) }}</el-col>
              <el-col :span="8" align="left" style="margin-top: 5px;">部件3: {{
                getRenovateStatus(getRenovateInfo(3))
              }}
              </el-col>
              <el-col :span="8" align="left" style="margin-top: 5px;">部件4: {{
                getRenovateStatus(getRenovateInfo(4))
              }}
              </el-col>
              <el-col :span="8" align="left" style="margin-top: 5px;">部件5: {{
                getRenovateStatus(getRenovateInfo(5))
              }}
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24"><h4 style="height: 5px;">支付信息</h4></el-col>
            </el-row>
            <el-row style="margin-right: 12px; margin-top: 10px;">
              <el-col :span="8" align="left">支付总额: ${{ form.PaymentInfo.PaymentAmount / 100 }}</el-col>
              <el-col :span="8" align="left">支付总次数: {{ form.PaymentInfo.PaymentCount }}</el-col>
              <el-col :span="8" align="left">RFM标签: {{ form.PaymentInfo.RFMTag }}</el-col>
              <el-col :span="8" align="left">最常支付价格: ${{ form.PaymentInfo.MostUsedPrice / 100 }}</el-col>
              <el-col :span="8" align="left">RFM周期最大付费: ${{ form.PaymentInfo.NaxPurchasePrice / 100 }}</el-col>
              <el-col :span="8" align="left">最近支付价格: ${{ form.PaymentInfo.LastPurchasePrice / 100 }}</el-col>

            </el-row>
            <el-row>
              <el-col :span="24"><h3 style="height: 5px;">邀请信息</h3></el-col>
            </el-row>
            <el-row style="margin-right: 12px; margin-top: 10px;">
              <el-col :span="8" align="left">邀请人数: {{ form.InviteInfo.InviteSuccessCount }}</el-col>
            </el-row>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button @click="open = false">关闭</el-button>
          </span>
        </el-dialog>

      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { listTPlayer } from '@/api/player/player'

export default {
  name: 'TPlayer',
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      playerList: [],
      userDetail: '',

      // 关系表类型

      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        player_id: undefined
      },
      // 表单参数
      form: {
        ProfileInfo: {
          UID: 88033,
          Name: '',
          AccType: 1,
          Country: '',
          CreateTime: 1651196376,
          LastLoginTime: 1651196465,
          OpenID: '',
          IsOnline: false,
          Mail: ''
        },
        DeviceInfo: {
          DeviceModel: 'editor',
          Os: 1,
          AppVersion: '0.1.15',
          AdjustID: ''
        },
        EpisodeInfo: {
          EpisodeID: 1,
          RenovateItemsInfo: []
        },
        InviteInfo: {
          InviteSuccessCount: 0
        },
        PaymentInfo: {
          PaymentCount: 0,
          PaymentAmount: 0,
          RFMTag: '',
          MostUsedPrice: 0,
          NaxPurchasePrice: 0,
          LastPurchasePrice: 0

        },
        imgUrl: 'https://platform-lookaside.fbsbx.com/platform/profilepic/?asid=1036709610527151&gaming_photo_type=unified_picture&ext=1666525401&hash=AeR4vvmU3VubNgBja-k'
      },
      // 表单校验
      rules: {
        // uid: [{ required: true, message: '玩家Id不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.loading = false
    // this.getList()
  },
  methods: {
    parseTime(timestamp) {
      if (!timestamp) return ''
      const date = new Date(timestamp * 1000)
      return date.toLocaleString()
    },
    /** 查询参数列表 */
    getList() {
      // if (this.queryParams.uid === undefined || this.form.uid === '') {
      //   this.msgError('数据量比较大，请输入必要参数')
      // } else {
      this.loading = true
      listTPlayer(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.playerList = response.data.list
        this.total = response.data.count
        this.loading = false
      }
      )
      // }
    },
    /** 用户详情 */
    getDetail() {
      this.open = true
    },
    getRenovateStatus(renovateItem) {
      // console.log('renovateItem:', renovateItem)
      if (renovateItem === undefined) {
        return '无'
      }
      var status = '无状态'
      if (renovateItem.Status === 1) {
        status = '正常'
      } else if (renovateItem.Status === 2) {
        status = '破损'
      }
      return '等级 ' + renovateItem.Level + ', ' + status
    },
    getPlatformName(platform) {
      if (platform === undefined) {
        return '无'
      }
      const platformMap = {
        0: '未知',
        1: 'Unity编辑器',
        2: 'iOS平台',
        3: 'Android平台',
        4: 'Windows平台',
        5: '小游戏平台',
        6: 'H5 Web平台',
        7: '智能穿戴平台',
        8: '主机游戏平台'
      }
      return platformMap[platform] || '未知'
    },
    getRenovateInfo(id) {
      for (var i = 0; i < this.form.EpisodeInfo.RenovateItemsInfo.length; i++) {
        var f = this.form.EpisodeInfo.RenovateItemsInfo[i]
        console.log('f.ItemID: ', f.ItemID)
        if (f.ItemID === id) {
          return f
        }
      }
      return undefined
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {}
      this.resetForm('form')
    },
    getImgList: function() {
      this.form[this.fileIndex] = this.$refs['fileChoose'].resultList[0].fullUrl
    },
    fileClose: function() {
      this.fileOpen = false
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.uid)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    }
  }
}
</script>
