
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :rules="rules" :inline="true" label-width="68px">
          <el-row>
            <el-col :span="24">
              <el-form-item label="玩家ID" prop="playerId"><el-input
                v-model="queryParams.playerId"
                placeholder="请输入玩家ID"
                clearable
                size="small"
                @keyup.enter.native="handleQuery"
              />
              </el-form-item>
              <el-form-item label="物品" prop="propType"><el-select
                v-model="queryParams.propType"
                placeholder="物品"
                clearable
                size="small"
              >
                <el-option
                  v-for="dict in propTypeOptions"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
              </el-form-item>
              <el-form-item label="来源" prop="sourceType"><el-select
                v-model="queryParams.sourceType"
                placeholder="来源"
                clearable
                size="small"
              >
                <el-option
                  v-for="dict in propSourceTypeOptions"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
              </el-form-item>
              <el-form-item label="版本" prop="appVersion"><el-input
                v-model="queryParams.appVersion"
                placeholder="请输入App版本"
                clearable
                size="small"
                @keyup.enter.native="handleQuery"
              />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="dateRange"
                size="small"
                type="datetimerange"
                :picker-options="pickerOptions"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                align="right"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-row>
        </el-form>

        <el-table v-loading="loading" :data="tBasePropOdsList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column
            label="玩家ID"
            align="left"
            prop="playerId"
            width="80"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="时间"
            align="left"
            width="160"
            prop="timeStampValue"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.timeStampValue) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="道具 id"
            align="center"
            prop="itemId"
            width="120"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="道具类型"
            align="center"
            prop="itemType"
            width="120"
          />
          <el-table-column
            label="当前数量"
            align="center"
            prop="curCount"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="变化数量"
            align="center"
            prop="deltaCount"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="来源"
            align="center"
            prop="srcType"
            width="120"
          >
            <!--            <template slot-scope="scope">-->
            <!--              {{ propSourceTypeFormat(scope.row) }}-->
            <!--            </template>-->
          </el-table-column>
          <el-table-column
            label="App版本"
            align="center"
            prop="appVersion"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="原始数据"
            align="center"
            prop="originalData"
            :show-overflow-tooltip="true"
          />
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { listTBasePropOds } from '@/api/player/proplog'

export default {
  name: 'TBasePropOds',
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      tBasePropOdsList: [],
      platformOptions: [],
      accTypeOptions: [],
      propTypeOptions: [],
      propSourceTypeOptions: [],
      // 日期范围
      dateRange: [],
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        platform: undefined,
        timeStampValue: undefined,
        appVersion: undefined,
        playerId: undefined,
        propType: undefined,
        sourceType: undefined,
        createdAtOrder: 'desc'
      },
      // 表单参数
      form: {
      },
      // 表单校验
      rules: {
        playerId: [{ required: true, message: '玩家ID不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    // this.getList()
    this.loading = false
    this.getDicts('platform').then(response => {
      this.platformOptions = response.data
    })
    this.getDicts('acc_type').then(response => {
      this.accTypeOptions = response.data
    })
    this.getDicts('prop_type').then(response => {
      this.propTypeOptions = response.data
    })
    this.getDicts('reward_source_type').then(response => {
      this.propSourceTypeOptions = response.data
    })
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      if (this.queryParams.playerId === undefined || this.queryParams.playerId === '') {
        this.msgError('数据量比较大，请输入必要参数')
      } else {
        this.loading = true
        listTBasePropOds(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
          this.tBasePropOdsList = response.data.list
          this.total = response.data.count
          this.loading = false
          this.reset()
        })
      }
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {

      }
      this.resetForm('form')
    },
    getImgList: function() {
      this.form[this.fileIndex] = this.$refs['fileChoose'].resultList[0].fullUrl
    },
    fileClose: function() {
      this.fileOpen = false
    },
    platformFormat(row) {
      return this.selectDictLabel(this.platformOptions, row.platform)
    },
    accTypeFormat(row) {
      return this.selectDictLabel(this.accTypeOptions, row.accType)
    },
    propTypeFormat(row) {
      return this.selectDictLabel(this.propTypeOptions, row.propType)
    },
    propSourceTypeFormat(row) {
      return this.selectDictLabel(this.propSourceTypeOptions, row.sourceType)
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.uid)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    }
  }
}
</script>
