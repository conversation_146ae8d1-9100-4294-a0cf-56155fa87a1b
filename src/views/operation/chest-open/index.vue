
<template>
  <BasicLayout>
    <template #wrapper>

      <el-card class="box-card">
        <div style="width: 50%;margin: 0 auto;">
          <el-form ref="form" :model="form" label-width="100px">
            <el-form-item label="指定关卡">
              <el-input-number v-model="form.EpID" :min="1" />
            </el-form-item>
            <el-form-item label="已收集卡牌数">
              <el-input-number v-model="form.GotCardNum" :min="0" />
            </el-form-item>
            <el-form-item label="指定宝箱">
              <el-input-number v-model="form.ChestID" :min="0" />
            </el-form-item>
            <el-form-item label="开宝箱数量">
              <el-input-number v-model="form.Count" :min="1" :max="10000" />
            </el-form-item>
            <el-form-item label="备注">
              <el-input v-model="form.remark" type="textarea" />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" :loading="chestOpen" @click="onSubmit">立即开始</el-button>
              <el-button>取消</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table v-loading="loading" :data="list">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column
            label="ID"
            align="center"
            prop="id"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="操作人"
            align="center"
            prop="createUser"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="下载地址"
            align="center"
            prop="URL"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <div @click="handelCopy(scope.row.URL)">{{ scope.row.URL }}</div>
            </template>
          </el-table-column>
          <el-table-column
            label="备注"
            align="center"
            prop="remark"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="创建时间"
            align="center"
            prop="createdAt"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="操作参数"
            align="center"
            prop="param"
            :show-overflow-tooltip="true"
          />
          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageIndex"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />

        </el-table></el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { chestOpen, getChestOpenList } from '@/api/admin/chest_open'

export default {
  name: 'ChestOpen',
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      chestOpen: false,
      // 总条数
      total: 0,
      list: [],
      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10
      },
      // 表单参数
      form: {
        EpID: 1,
        GotCardNum: 0,
        ChestID: 0,
        Count: 1
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      getChestOpenList(this.queryParams).then(response => {
        this.list = response.data.list
        this.total = response.data.count
        this.loading = false
      }
      )
    },
    onSubmit() {
      this.chestOpen = true
      chestOpen(this.form).then(response => {
        console.log(response)
        this.$message({
          showClose: true,
          message: '操作完成',
          type: 'success'
        })
        this.chestOpen = false
        this.getList()
      }).catch(response => {
        console.log(response)
        this.$message({
          showClose: true,
          message: '操作失败，请检查参数',
          type: 'error'
        })
        this.chestOpen = false
        this.getList()
      })
    },
    handelCopy(url) {
      /**
       navigator.clipboard.writeText(url).then(() => {
        this.$Message.success('复制成功')
      })
       **/
      const oInput = document.createElement('input')
      oInput.value = url
      document.body.appendChild(oInput)
      oInput.select() // 选择对象
      document.execCommand('Copy') // 执行浏览器复制命令
      this.$message({
        showClose: true,
        message: '复制成功',
        type: 'success'
      })
      oInput.remove()
    }
  }
}
</script>
