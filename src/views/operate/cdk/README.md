# CDK管理系统

## 功能概述

这是一个完整的CDK（兑换码）管理系统，基于Vue.js和Element UI开发，提供了CDK批次的创建、查看、导出和作废等功能。

## 主要功能

### 1. CDK批次管理
- **查看批次列表**: 显示所有CDK批次，包含批次ID、生成数量、使用限制、时间等信息
- **状态筛选**: 可以按状态（有效/已作废）筛选批次
- **分页显示**: 支持分页浏览，提高大数据量下的性能

### 2. 创建CDK批次
- **基本信息**: 批次名称、描述
- **有效时间**: 设置CDK的有效期
- **奖励配置**: 支持添加多个奖励道具，每个奖励包含道具ID和数量
- **生成方式**: 
  - 自动生成：指定数量，系统自动生成CDK
  - 手动输入：手动输入CDK列表
- **使用限制**: 设置每个CDK的使用次数限制（-1表示无限制）

### 3. CDK记录查看
- **二级查看**: 点击"查看"按钮可以查看该批次下的所有CDK记录
- **状态显示**: 显示每个CDK的使用状态（已使用/未使用）
- **分页支持**: CDK记录也支持分页显示

### 4. 导出功能
- **CSV导出**: 支持将CDK记录导出为CSV文件
- **包含信息**: CDK码、生成时间、使用状态
- **文件命名**: 自动生成带时间戳的文件名

### 5. 批次作废
- **状态管理**: 支持作废CDK批次
- **确认机制**: 作废前需要确认操作
- **状态显示**: 已作废的批次会显示相应状态

## 技术特点

### 1. 响应式设计
- 基于Element UI组件库
- 支持不同屏幕尺寸
- 良好的用户体验

### 2. 数据验证
- 前端表单验证
- 业务逻辑验证
- 错误提示友好

### 3. 性能优化
- 分页加载
- 按需查询
- 合理的数据结构

### 4. 用户体验
- 加载状态提示
- 操作反馈
- 错误处理

## API接口

### 1. 查询CDK批次
```
GET /api/v1/cdk/batches?pageIndex=1&pageSize=10&status=1
```

### 2. 查询CDK记录
```
GET /api/v1/cdk/records?pageIndex=1&pageSize=10&batch_id=10
```

### 3. 创建CDK批次
```
POST /api/v1/cdk/batch
```

### 4. 作废CDK批次
```
PUT /api/v1/cdk/batch/disable?batch_id=4
```

## 文件结构

```
src/views/operate/cdk/
├── index.vue          # 主页面组件
└── README.md          # 说明文档

src/api/operate/
└── cdk.js            # API接口定义
```

## 使用说明

1. **访问页面**: 在系统中导航到CDK管理页面
2. **查看批次**: 页面加载后自动显示CDK批次列表
3. **创建批次**: 点击"新增CDK批次"按钮，填写相关信息
4. **查看记录**: 点击批次行的"查看"按钮查看具体CDK记录
5. **导出CDK**: 点击"导出"按钮下载CDK列表
6. **作废批次**: 点击"作废"按钮使批次失效

## 注意事项

1. **时间格式**: 所有时间都使用Unix时间戳格式
2. **奖励配置**: 至少需要添加一个奖励道具
3. **CDK格式**: 手动输入的CDK长度应在6-20个字符之间
4. **权限控制**: 确保用户有相应的操作权限
5. **数据备份**: 重要操作前建议备份数据

## 扩展功能

可以根据需要添加以下功能：
- CDK使用记录查询
- 批量操作功能
- 更多导出格式支持
- 高级筛选条件
- 操作日志记录
