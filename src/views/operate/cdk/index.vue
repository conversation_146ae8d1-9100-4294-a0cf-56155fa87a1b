<template>
  <div>
    <BasicLayout>
      <template #wrapper>
        <el-card class="box-card">
          <!-- 搜索区域 -->
          <el-form ref="queryForm" :model="queryParams" :inline="true">
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="queryParams.status"
                clearable
                placeholder="请选择状态"
                @change="handleQuery"
              >
                <el-option
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              <el-button
                type="primary"
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
              >新增CDK批次
              </el-button>
            </el-form-item>
          </el-form>

          <!-- 表格区域 -->
          <el-table v-loading="loading" :data="batchList">
            <el-table-column type="index" label="序号" width="55" align="center" />
            <el-table-column
              label="批次"
              align="center"
              prop="id"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="批次描述"
              align="center"
              prop="description"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="生成CDK数量"
              align="center"
              prop="cdk_count"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="单个CDK兑换次数"
              align="center"
              width="120"
            >
              <template slot-scope="scope">
                {{ scope.row.cdk_limit === -1 ? '无限制' : scope.row.cdk_limit }}
              </template>
            </el-table-column>
            <el-table-column label="生成时间" align="center" width="160">
              <template slot-scope="scope">
                <div v-if="scope.row.created_at">
                  {{ formatTime(scope.row.created_at) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="CDK有效时间开始" align="center" width="160">
              <template slot-scope="scope">
                <div v-if="scope.row.start_time">
                  {{ formatTime(scope.row.start_time) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="CDK有效时间结束" align="center" width="160">
              <template slot-scope="scope">
                <div v-if="scope.row.end_time">
                  {{ formatTime(scope.row.end_time) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="状态" align="center" width="100">
              <template slot-scope="scope">
                <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
                  {{ scope.row.status === 1 ? '有效' : '已作废' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="下载导出" align="center" width="100">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  @click="handleExport(scope.row)"
                >导出
                </el-button>
              </template>
            </el-table-column>
            <el-table-column label="查看日志" align="center" width="100">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  @click="handleViewRecords(scope.row)"
                >查看
                </el-button>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="100">
              <template slot-scope="scope">
                <el-popconfirm
                  v-if="scope.row.status === 1"
                  class="delete-popconfirm"
                  title="确认要作废该CDK批次吗?"
                  confirm-button-text="作废"
                  @onConfirm="handleDisable(scope.row)"
                >
                  <el-button
                    slot="reference"
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                  >作废
                  </el-button>
                </el-popconfirm>
                <span v-else class="disabled-text">已作废</span>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageIndex"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />

          <!-- 创建CDK批次弹窗 -->
          <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="120px">
              <el-form-item label="批次名称" prop="batch_name">
                <el-input v-model="form.batch_name" placeholder="请输入批次名称" />
              </el-form-item>
              <el-form-item label="描述" prop="description">
                <el-input v-model="form.description" type="textarea" placeholder="请输入描述" />
              </el-form-item>
              <el-form-item label="有效时间" prop="dateRange">
                <el-date-picker
                  v-model="dateRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="timestamp"
                  :default-time="['00:00:00', '23:59:59']"
                />
              </el-form-item>
              <el-form-item label="奖励配置" prop="rewards">
                <el-button type="primary" size="mini" @click="addReward">添加奖励</el-button>
                <div v-if="form.rewards.length === 0" style="margin-top: 10px; color: #999;">
                  请至少添加一个奖励
                </div>
                <div v-for="(reward, index) in form.rewards" :key="index" class="reward-item">
                  <el-row :gutter="10">
                    <el-col :span="8">
                      <el-input
                        v-model.number="reward.item_id"
                        placeholder="道具ID"
                        type="number"
                        :min="1"
                      />
                    </el-col>
                    <el-col :span="8">
                      <el-input
                        v-model.number="reward.item_count"
                        placeholder="数量"
                        type="number"
                        :min="1"
                      />
                    </el-col>
                    <el-col :span="8">
                      <el-button type="danger" size="mini" @click="removeReward(index)">删除</el-button>
                    </el-col>
                  </el-row>
                </div>
              </el-form-item>
              <el-form-item label="生成方式" prop="generation_option">
                <el-radio-group v-model="form.generation_option">
                  <el-radio :label="1">自动生成</el-radio>
                  <el-radio :label="2">手动输入</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item v-if="form.generation_option === 1" label="生成数量" prop="generation_count">
                <el-input-number v-model="form.generation_count" :min="1" :max="10000" />
              </el-form-item>
              <el-form-item v-if="form.generation_option === 2" label="手动CDK">
                <el-input
                  v-model="manualCdksText"
                  type="textarea"
                  :rows="5"
                  placeholder="请输入CDK，每行一个"
                  class="manual-cdk-textarea"
                />
                <div style="margin-top: 5px; color: #999; font-size: 12px;">
                  提示：每行输入一个CDK，长度应在6-20个字符之间
                </div>
              </el-form-item>
              <el-form-item label="使用次数限制" prop="cdk_use_limit">
                <el-input-number v-model="form.cdk_use_limit" :min="-1" />
                <span style="margin-left: 10px; color: #999;">-1表示无限制</span>
              </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
              <el-button type="primary" @click="submitForm">确 定</el-button>
              <el-button @click="cancel">取 消</el-button>
            </div>
          </el-dialog>

          <!-- 查看CDK记录弹窗 -->
          <el-dialog title="CDK记录" :visible.sync="recordsOpen" width="1000px" append-to-body>
            <el-table v-loading="recordsLoading" :data="recordsList" max-height="400">
              <el-table-column type="index" label="序号" width="55" align="center" />
              <el-table-column
                label="CDK"
                align="center"
                prop="cdk"
                :show-overflow-tooltip="true"
              />
              <el-table-column label="生成时间" align="center" width="160">
                <template slot-scope="scope">
                  <div v-if="scope.row.created_at">
                    {{ formatTime(scope.row.created_at) }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="使用状态" align="center" width="100">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.used ? 'success' : 'info'">
                    {{ scope.row.used ? '已使用' : '未使用' }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>

            <!-- CDK记录分页 -->
            <pagination
              v-show="recordsTotal>0"
              :total="recordsTotal"
              :page.sync="recordsQueryParams.pageIndex"
              :limit.sync="recordsQueryParams.pageSize"
              @pagination="getRecordsList"
            />
          </el-dialog>
        </el-card>
      </template>
    </BasicLayout>
  </div>
</template>

<script>
import store from '../../../store'
import { listCdkBatches, listCdkRecords, createCdkBatch, disableCdkBatch } from '@/api/operate/cdk'

export default {
  name: 'CdkManage',
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      recordsLoading: false,
      // 总条数
      total: 0,
      recordsTotal: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      recordsOpen: false,
      // CDK批次列表
      batchList: [],
      // CDK记录列表
      recordsList: [],
      // 状态选项
      statusOptions: [{
        value: 1,
        label: '有效'
      }, {
        value: 2,
        label: '已作废'
      }],
      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        status: undefined
      },
      // CDK记录查询参数
      recordsQueryParams: {
        pageIndex: 1,
        pageSize: 10,
        batch_id: undefined
      },
      // 表单参数
      form: {
        batch_name: '',
        description: '',
        start_time: undefined,
        end_time: undefined,
        rewards: [],
        generation_option: 1,
        generation_count: 10,
        cdk_use_limit: -1,
        manual_cdks: []
      },
      dateRange: [],
      manualCdksText: '',
      // 表单校验
      rules: {
        batch_name: [
          { required: true, message: '批次名称不能为空', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '描述不能为空', trigger: 'blur' },
          { max: 200, message: '描述不能超过200个字符', trigger: 'blur' }
        ],
        dateRange: [{ required: true, message: '请选择有效时间', trigger: 'change' }],
        generation_option: [{ required: true, message: '请选择生成方式', trigger: 'change' }],
        generation_count: [
          { required: true, message: '生成数量不能为空', trigger: 'blur' },
          { type: 'number', min: 1, max: 10000, message: '生成数量必须在1-10000之间', trigger: 'blur' }
        ],
        cdk_use_limit: [
          { required: true, message: '使用次数限制不能为空', trigger: 'blur' },
          { type: 'number', min: -1, message: '使用次数限制不能小于-1', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    formatTime(timestamp) {
      const date = new Date(timestamp * 1000)
      return date.toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }).replace(/\//g, '-')
    },
    /** 查询CDK批次列表 */
    getList() {
      this.loading = true
      // 构建查询参数，过滤掉undefined的值
      const params = {}
      Object.keys(this.queryParams).forEach(key => {
        if (this.queryParams[key] !== undefined && this.queryParams[key] !== '') {
          params[key] = this.queryParams[key]
        }
      })

      console.log('查询参数:', params) // 调试用，可以删除

      listCdkBatches(params).then(response => {
        this.batchList = response.data.list || []
        this.total = response.data.count || 0
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    /** 查询CDK记录列表 */
    getRecordsList() {
      this.recordsLoading = true
      listCdkRecords(this.recordsQueryParams).then(response => {
        this.recordsList = response.data.list || []
        this.recordsTotal = response.data.count || 0
        this.recordsLoading = false
      }).catch(() => {
        this.recordsLoading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.dateRange = []
      this.manualCdksText = ''
      this.form = {
        batch_name: '',
        description: '',
        start_time: undefined,
        end_time: undefined,
        rewards: [],
        generation_option: 1,
        generation_count: 10,
        cdk_use_limit: -1,
        manual_cdks: []
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      console.log('搜索时的状态值:', this.queryParams.status) // 调试用
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      // 手动重置查询参数
      this.queryParams = {
        pageIndex: 1,
        pageSize: 10,
        status: undefined
      }
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '创建CDK批次'
    },
    /** 查看CDK记录 */
    handleViewRecords(row) {
      this.recordsQueryParams.batch_id = row.id
      this.recordsQueryParams.pageIndex = 1
      this.recordsOpen = true
      this.getRecordsList()
    },
    /** 导出CDK */
    handleExport(row) {
      // 获取该批次的所有CDK记录
      const exportParams = {
        pageIndex: 1,
        pageSize: 10000, // 获取所有记录
        batch_id: row.id
      }

      this.loading = true
      listCdkRecords(exportParams).then(response => {
        const records = response.data.list || []
        if (records.length === 0) {
          this.msgError('该批次没有CDK记录')
          this.loading = false
          return
        }

        // 创建CSV内容
        let csvContent = 'CDK码,生成时间,使用状态\n'
        records.forEach(record => {
          const time = this.formatTime(record.created_at)
          const status = record.used ? '已使用' : '未使用'
          csvContent += `${record.cdk},${time},${status}\n`
        })

        // 创建下载链接
        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
        const link = document.createElement('a')
        const url = URL.createObjectURL(blob)
        link.setAttribute('href', url)
        link.setAttribute('download', `CDK批次${row.id}_${new Date().getTime()}.csv`)
        link.style.visibility = 'hidden'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        this.msgSuccess('导出成功')
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    /** 作废CDK批次 */
    handleDisable(row) {
      disableCdkBatch({ batch_id: row.id }).then(response => {
        if (response.code === 200) {
          this.msgSuccess('作废成功')
          this.getList()
        } else {
          this.msgError(response.msg || '作废失败')
        }
      })
    },
    /** 添加奖励 */
    addReward() {
      this.form.rewards.push({
        item_id: '',
        item_count: ''
      })
    },
    /** 删除奖励 */
    removeReward(index) {
      this.form.rewards.splice(index, 1)
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          // 验证时间范围
          if (!this.dateRange || this.dateRange.length !== 2) {
            this.msgError('请选择有效时间范围')
            return
          }

          // 验证奖励配置
          if (this.form.rewards.length === 0) {
            this.msgError('请至少添加一个奖励')
            return
          }

          // 验证奖励配置的完整性
          for (let i = 0; i < this.form.rewards.length; i++) {
            const reward = this.form.rewards[i]
            if (!reward.item_id || !reward.item_count) {
              this.msgError(`第${i + 1}个奖励的道具ID和数量不能为空`)
              return
            }
            if (reward.item_id <= 0 || reward.item_count <= 0) {
              this.msgError(`第${i + 1}个奖励的道具ID和数量必须大于0`)
              return
            }
          }

          // 处理手动CDK
          if (this.form.generation_option === 2) {
            this.form.manual_cdks = this.manualCdksText.split('\n').filter(cdk => cdk.trim())
            if (this.form.manual_cdks.length === 0) {
              this.msgError('请输入至少一个CDK')
              return
            }
            // 验证CDK格式（可以根据需要添加更严格的验证）
            for (let i = 0; i < this.form.manual_cdks.length; i++) {
              const cdk = this.form.manual_cdks[i].trim()
              if (cdk.length < 6 || cdk.length > 20) {
                this.msgError(`第${i + 1}个CDK长度应在6-20个字符之间`)
                return
              }
            }
          }

          // 构建请求参数
          const data = {
            batch_name: this.form.batch_name,
            description: this.form.description,
            start_time: Math.floor(this.dateRange[0] / 1000),
            end_time: Math.floor(this.dateRange[1] / 1000),
            rewards: this.form.rewards,
            generation_option: this.form.generation_option,
            generation_count: this.form.generation_count,
            cdk_use_limit: this.form.cdk_use_limit,
            manual_cdks: this.form.manual_cdks
          }

          createCdkBatch(data).then(response => {
            if (response.code === 200) {
              this.msgSuccess('创建成功')
              this.open = false
              this.getList()
            } else {
              this.msgError(response.msg || '创建失败')
            }
          }).catch(() => {
            this.msgError('创建失败，请检查网络连接')
          })
        }
      })
    }
  }
}
</script>

<style scoped>
.disabled-text {
  color: #999;
}

.box-card {
  margin: 10px;
}

.el-form-item {
  margin-bottom: 18px;
}

.el-table {
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}

.delete-popconfirm {
  margin-left: 10px;
}

/* 奖励配置样式 */
.reward-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
  margin-top: 10px;
  background-color: #fafafa;
}

.reward-item:hover {
  border-color: #c0c4cc;
}

/* 手动CDK输入框样式 */
.manual-cdk-textarea {
  font-family: 'Courier New', monospace;
}

/* 状态标签样式 */
.el-tag {
  margin-right: 5px;
}
</style>
