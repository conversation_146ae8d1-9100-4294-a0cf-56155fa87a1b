<template>
  <div>
    <BasicLayout>
      <template #wrapper>
        <el-card class="box-card">
          <el-form ref="queryForm" :model="queryParams" :inline="true">
            <el-form-item label="拍脸图ID" prop="id">
              <el-input
                v-model="queryParams.id"
                placeholder="请输入id"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="启用状态" prop="enable">
              <el-select v-model="queryParams.enable" clearable placeholder="请选择">
                <el-option
                  v-for="item in enableOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              <el-button
                type="primary"
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
              >新增
              </el-button>
            </el-form-item>
          </el-form>

          <el-table v-loading="loading" :data="popupList" @selection-change="handleSelectionChange">
            <el-table-column type="index" label="序号" width="55" align="center" />
            <el-table-column
              label="ID"
              align="center"
              prop="id"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="优先级"
              align="center"
              prop="priority"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="启用状态"
              align="center"
              width="100"
            >
              <template slot-scope="scope">
                <el-tag :type="scope.row.enable === true ? 'success' : 'info'">
                  {{ scope.row.enable === true ? '启用' : '关闭' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              label="显示策略"
              align="center"
              width="100"
              :formatter="formatStrategy"
            />
            <el-table-column label="内容" align="center" width="100">
              <template slot-scope="scope">
                <el-image
                  style="width: 100px; height: 100px"
                  :src="JSON.parse(scope.row.ann_content).image_url"
                  :preview-src-list="[JSON.parse(scope.row.ann_content).image_url]"
                />
              </template>
            </el-table-column>
            <el-table-column label="生效时间" align="center">
              <template slot-scope="scope">
                <div v-if="scope.row.start_time">
                  {{ formatTime(scope.row.start_time) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="截止时间" align="center">
              <template slot-scope="scope">
                <div v-if="scope.row.end_time">
                  {{ formatTime(scope.row.end_time) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button
                  slot="reference"
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                >修改
                </el-button>
                <el-popconfirm
                  class="delete-popconfirm"
                  title="确认要删除吗?"
                  confirm-button-text="删除"
                  @onConfirm="handleDelete(scope.row)"
                >
                  <el-button
                    slot="reference"
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                  >删除
                  </el-button>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageIndex"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />

          <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="120px">
              <el-form-item label="优先级" prop="priority">
                <el-input-number v-model="form.priority" :min="1" :max="100" />
              </el-form-item>
              <el-form-item label="启用状态" prop="enable">
                <el-radio-group v-model="form.enable">
                  <el-radio :label="true">启用</el-radio>
                  <el-radio :label="false">关闭</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="渠道ID" prop="channel_id">
                <el-input-number v-model="form.channel_id" :disabled="true" :min="1000" :max="9999" />
              </el-form-item>
              <el-form-item label="弹窗样式" prop="pop_style">
                <el-select v-model="form.pop_style" placeholder="请选择弹窗样式">
                  <el-option label="通用" :value="1" />
                  <el-option label="特殊关闭按钮" :value="2" />
                </el-select>
              </el-form-item>
              <el-form-item label="图片URL" prop="image_url">
                <el-input v-model="form.image_url" placeholder="请输入图片URL" />
              </el-form-item>
              <el-form-item label="跳转类型" prop="jump_type">
                <el-select v-model="form.jump_type" placeholder="请选择跳转类型">
                  <el-option label="无" :value="0" />
                  <el-option label="商城" :value="1" />
                  <el-option label="活动中心" :value="2" />
                  <el-option label="公告中心" :value="3" />
                  <el-option label="选场中心" :value="4" />
                </el-select>
              </el-form-item>
              <el-form-item label="跳转参数" prop="jump_args">
                <el-input v-model="form.jump_args" placeholder="请输入跳转参数" />
              </el-form-item>
              <el-form-item label="显示策略" prop="display_strategy">
                <el-select v-model="form.display_strategy" placeholder="请选择显示策略">
                  <el-option label="每日一次" :value="1" />
                  <el-option label="历史一次" :value="2" />
                  <el-option label="每次登录" :value="3" />
                </el-select>
              </el-form-item>
              <el-form-item label="最低版本" prop="min_version">
                <el-input v-model="form.min_version" placeholder="请输入最低版本，例如：1.0.0" />
              </el-form-item>
              <el-form-item label="生效时间" prop="dateRange">
                <el-date-picker
                  v-model="dateRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="timestamp"
                  :default-time="['00:00:00', '23:59:59']"
                />
              </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
              <el-button type="primary" @click="submitForm">确 定</el-button>
              <el-button @click="cancel">取 消</el-button>
            </div>
          </el-dialog>
        </el-card>
      </template>
    </BasicLayout>
  </div>
</template>

<script>
import store from '../../../store'
import XLSX from 'xlsx'
import { addOperatePopup, deleteOperatePopup, listOperatePopup, updateOperatePopup } from '@/api/operate/mail'

export default {
  name: 'OperateMaster',
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      open2: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      popupList: [],
      mailTypeOptions: [], mailActiveOptions: [], mailOperateSourceOptions: [], languageTypeOptions: [],
      // 关系表类型
      propTypeOptions: [],

      // Excel导入
      configAllHeader: {},
      configAllBody: {},
      configFieldMasterList: [],
      configSheetNames: [], // 导入Excel的所有Sheet名
      activeName: '',
      importFileName: '', // 文件名
      // 用户导入参数
      uploadFrame: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false
      },
      excelData: {
        planetId: null,
        sheet: null,
        header: null,
        results: null
      },

      enableOptions: [{
        value: 1,
        label: '启用'
      }, {
        value: 2,
        label: '关闭'
      }],
      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        planetId: undefined,
        enable: undefined
      },
      deleteParams: {
        id: undefined
      },
      // 表单参数
      form: {
        id: undefined,
        priority: 1,
        channel_id: 1001,
        pop_style: 1,
        image_url: '',
        jump_type: 100,
        jump_args: '',
        display_strategy: 1,
        min_version: '1.0.0',
        start_time: undefined,
        end_time: undefined,
        enable: false
      },
      // 导入表单参数
      formImport: {
        uid: ''
      },
      dateRange: [],
      pickerOptions2: {
        shortcuts: [{
          text: '往后一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            end.setTime(end.getTime() + 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '往后一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            end.setTime(end.getTime() + 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '往后三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            end.setTime(end.getTime() + 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      value6: '',
      value7: '',
      // 表单校验
      rules: {
        priority: [{ required: true, message: '优先级不能为空', trigger: 'blur' }],
        channel_id: [{ required: true, message: '渠道ID不能为空', trigger: 'blur' }],
        pop_style: [{ required: true, message: '弹窗样式不能为空', trigger: 'blur' }],
        image_url: [{ required: true, message: '图片URL不能为空', trigger: 'blur' }],
        display_strategy: [{ required: true, message: '显示策略不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    formatStrategy(row) {
      try {
        const condition = JSON.parse(row.ann_conditions)
        switch (condition.display_strategy?.toString()) {
          case '1':
            return '每日一次'
          case '2':
            return '历史一次'
          case '3':
            return '每次登录'
          default:
            return '未知策略'
        }
      } catch (e) {
        console.error('JSON  解析失败', e)
        return '数据异常'
      }
    },
    formatTime(timestamp) {
      const date = new Date(timestamp * 1000)
      return date.toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }).replace(/\//g, '-')
    },
    /** 查询参数列表 */
    getList() {
      this.loading = true
      this.queryParams.planetId = store.getters.project[2]
      listOperatePopup(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.popupList = response.data.list
        console.log(response)
        this.total = response.data.count
        this.loading = false
      }
      )
    },
    jsonView(str) {
      if (str == null || str.length === 0) {
        return ''
      } else {
        return JSON.parse(str)
      }
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.dateRange = []
      this.form = {
        id: undefined,
        priority: 1,
        channel_id: store.getters.project[2],
        pop_style: 1,
        image_url: '',
        jump_type: undefined,
        jump_args: '',
        display_strategy: 1,
        min_version: undefined,
        start_time: undefined,
        end_time: undefined,
        enable: 1
      }
      this.resetForm('form')
    },
    getImgList: function() {
      this.form[this.fileIndex] = this.$refs['fileChoose'].resultList[0].fullUrl
    },
    selectItem: function(value, po) {
      // console.log('selectItem', po)
      for (let index = 0; index < this.propTypeOptions.length; index++) {
        const element = this.propTypeOptions[index]
        if (element.value === value) {
          console.log('selectItem', element.value, value, element.hadPropID)
          po.showPropID = element.hadPropID
          break
        }
      }
      // po.propId = value
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加拍脸图'
      this.isEdit = false
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.open = true
      this.title = '修改拍脸图'
      this.isEdit = true

      // 填充表单数据
      this.form.id = row.id
      this.form.priority = row.priority
      this.form.channel_id = row.channel_id
      this.form.pop_style = row.pop_style
      this.form.enable = row.enable

      // 解析JSON字符串
      try {
        const content = JSON.parse(row.ann_content)
        this.form.image_url = content.image_url

        const action = JSON.parse(row.ann_action)
        this.form.jump_type = action.jump_type
        this.form.jump_args = action.jump_args

        const conditions = JSON.parse(row.ann_conditions)
        this.form.display_strategy = parseInt(conditions.display_strategy)
        this.form.min_version = conditions.min_version
      } catch (e) {
        console.error('JSON解析失败', e)
      }

      // 设置时间范围
      this.dateRange = [row.start_time * 1000, row.end_time * 1000]
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (!this.dateRange || this.dateRange.length !== 2) {
            this.msgError('请选择生效时间范围')
            return
          }

          // 构建请求参数
          const data = {
            priority: this.form.priority,
            channel_id: this.form.channel_id,
            pop_style: this.form.pop_style,
            enable: this.form.enable,
            ann_content: JSON.stringify({
              image_url: this.form.image_url
            }),
            ann_action: JSON.stringify({
              jump_type: this.form.jump_type,
              jump_args: this.form.jump_args
            }),
            ann_conditions: JSON.stringify({
              display_strategy: this.form.display_strategy,
              min_version: this.form.min_version
            }),
            start_time: Math.floor(this.dateRange[0] / 1000),
            end_time: Math.floor(this.dateRange[1] / 1000)
          }

          // 如果是编辑模式，添加ID
          if (this.isEdit) {
            data.id = this.form.id
            updateOperatePopup(data).then(response => {
              if (response.code === 200) {
                this.msgSuccess('修改成功')
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg || '修改失败')
              }
            })
          } else {
            addOperatePopup(data).then(response => {
              if (response.code === 200) {
                this.msgSuccess('新增成功')
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg || '新增失败')
              }
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id
      this.$confirm('确认要删除该拍脸图吗?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteParams.id = id
        deleteOperatePopup(this.deleteParams).then(response => {
          if (response.code === 200) {
            this.msgSuccess('删除成功')
            this.getList()
          } else {
            this.msgError(response.msg || '删除失败')
          }
        })
      }).catch(() => {
      })
    },
    upload(rawFile) {
      this.$refs['excel-upload-input'].value = null // fix can't select the same excel

      if (!this.beforeUpload) {
        this.readerData(rawFile)
        return
      }

      const before = this.beforeUpload(rawFile)
      if (before) {
        this.readerData(rawFile)
      }
    },
    changeTab(sheetName) {
      // 首先读取第一个Sheet
      this.loading = true
      this.selectSheetName = sheetName
      this.activeName = sheetName
      const results = this.configAllBody[sheetName]

      // console.log('changeTab header', header)
      // console.log('changeTab results', results)

      if (results === 'undefined' || results.length === 0) {
        return
      }

      var formatStr = ''
      var count = 1
      for (let index = 0; index < results.length; index++) {
        if (index <= 1) {
          continue
        }
        const element = results[index]
        formatStr = formatStr + element + '        '
        if (count > 0 && count % 10 === 0) {
          // console.log(count + '换行' + index)
          formatStr += '\n'
        }
        count++
      }

      this.formImport.uid = formatStr
      this.configFieldMasterList = results
      // this.title = '导入玩家UID' + this.importFileName
      this.title = '导入玩家UID'
      this.open2 = true
      this.isEdit = false
      this.loading = false
    },
    readerData(rawFile) {
      this.loading = true
      return new Promise((resolve) => {
        const reader = new FileReader()
        reader.onload = e => {
          const data = e.target.result
          const workbook = XLSX.read(data, { type: 'array' })
          this.configSheetNames = workbook.SheetNames

          for (let index = 0; index < this.configSheetNames.length; index++) {
            const sheetName = workbook.SheetNames[index]
            const aHeadCenum = sheetName.startsWith('c.')

            const worksheet = workbook.Sheets[sheetName]
            // const header = this.getHeaderRow(worksheet, { header: 1, defval: '' })
            const header = this.getHeaderRow(worksheet, { header: 1 })
            const result = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
            if (aHeadCenum) {
              // 转换Const为列
              var newHeader = []
              var newRow = []
              for (let index = 1; index < result.length; index++) { // 过滤const的标题
                const element = result[index]
                newRow[index - 1] = element[0]
                newHeader[index - 1] = element[2]
              }
              var newResult = []
              newResult.push(newHeader)
              newResult.push(newRow)

              this.configAllHeader[sheetName] = newHeader
              this.configAllBody[sheetName] = newResult
            } else {
              this.configAllHeader[sheetName] = header
              this.configAllBody[sheetName] = result
            }
            // console.log('this.configAllBody', this.configAllBody)
          }

          // 默认显示第一个sheet
          const firstSheetName = workbook.SheetNames[0]
          this.changeTab(firstSheetName)

          this.loading = false
          resolve()
        }
        reader.readAsArrayBuffer(rawFile)
      })
    },
    getHeaderRow(sheet) {
      const headers = []
      const range = XLSX.utils.decode_range(sheet['!ref'])
      let C
      const R = range.s.r
      /* start in the first row */
      for (C = range.s.r; C <= range.e.c; ++C) { /* walk every column in the range */
        const cell = sheet[XLSX.utils.encode_cell({ c: C, r: R })]
        /* find the cell in the first row */
        // let hdr = 'UNKNOWN ' + C // <-- replace with your desired default
        let hdr = '' // <-- empty default
        if (cell && cell.t) hdr = XLSX.utils.format_cell(cell)
        headers.push(hdr)
      }
      return headers
    },
    isExcel(file) {
      return /\.(xlsx|xls|csv)$/.test(file.name)
    },
    // methods中写方法
    cellStyle(data) {
      var fieldRuleId = data.row.fieldRuleId
      if (fieldRuleId !== '') {
        var checkIdArray = JSON.parse(fieldRuleId)
        if (checkIdArray.length === 0) {
          return 'color: gray'
        }
        return 'color: green'
      }

      return 'color: gray'
    }
  }
}
</script>

<style>
.text {
  font-size: 14px;
}

.item {
  margin-bottom: 18px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both
}

.row-con {
  display: flex;
  flex-flow: row wrap;
}

.row-con .card {
  height: 100%;
}

.box-card-sub {
  width: 900px;
}
</style>
