
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="项目" prop="productId"><el-select
            v-model="queryParams.productId"
            placeholder="请选项目"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in projectList"
              :key="parseInt(dict.id)"
              :label="dict.name"
              :value="parseInt(dict.product_id)"
            />
          </el-select>
          </el-form-item>
          <el-form-item label="渠道名称" prop="name"><el-input
            v-model="queryParams.name"
            placeholder="请输入产品名称"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              v-permisaction="['admin:planet:add']"
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-permisaction="['admin:planet:edit']"
              type="success"
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
            >修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-permisaction="['admin:planet:remove']"
              type="danger"
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
            >删除
            </el-button>
          </el-col>
        </el-row>

        <el-table v-loading="loading" :data="planetList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="45" align="center" />
          <el-table-column
            label="ID"
            align="center"
            prop="id"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="项目"
            align="left"
            prop="productId"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              {{ envProductName(scope.row) }}
            </template>
          </el-table-column>
          <el-table-column
            label="渠道ID"
            align="left"
            prop="planetId"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="渠道名"
            align="left"
            prop="name"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="是否海外"
            align="center"
            prop="env"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <el-tag>{{ inlandContent(scope.row) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="别名/简称"
            align="center"
            prop="aliasName"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="关联信息"
            align="center"
            prop="relationName"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要修改吗?"
                confirm-button-text="修改"
                @onConfirm="handleUpdate(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['admin:planet:edit']"
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                >修改
                </el-button>
              </el-popconfirm>
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要删除吗?"
                confirm-button-text="删除"
                @onConfirm="handleDelete(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['admin:env:remove']"
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                >删除
                </el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="750px">
          <el-form ref="form" :model="form" :rules="rules" label-width="120px">
            <el-form-item label="项目" prop="product_id">
              <el-select
                v-model="form.productId"
                placeholder="请选择项目"
              >
                <el-option
                  v-for="dict in projectList"
                  :key="dict.id"
                  :label="dict.name+'('+dict.product_id+')'"
                  :value="dict.product_id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="渠道ID" prop="planetId">
              <el-input
                v-model="form.planetId"
                placeholder="渠道ID"
              />
            </el-form-item>
            <el-form-item label="渠道名称" prop="name">
              <el-input
                v-model="form.name"
                placeholder="渠道名称"
              />
            </el-form-item>
            <el-form-item label="别名" prop="aliasName">
              <el-input
                v-model="form.aliasName"
                placeholder="别名"
              />
            </el-form-item>
            <el-form-item label="关联信息" prop="relationName">
              <el-input
                v-model="form.relationName"
                placeholder="关联信息"
              />
            </el-form-item>
            <el-form-item label="描述" prop="describe">
              <el-input
                v-model="form.describe"
                placeholder="描述及渠道信息"
              />
            </el-form-item>
            <el-form-item label="是否海外" prop="isInland">
              <el-select
                v-model="form.isInland"
                placeholder="状态"
                size="small"
              >
                <el-option
                  v-for="dict in inlandOptions"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="AppID" prop="app_id">
              <el-input
                v-model="form.app_id"
                placeholder="app_id"
              />
            </el-form-item>
            <el-form-item label="Security" prop="app_security">
              <el-input
                v-model="form.app_security"
                placeholder="app_security"
              />
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="form.status"
                placeholder="状态"
                size="small"
              >
                <el-option
                  v-for="dict in statusOptions"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { addPlanet, delPlanet, getPlanet, listPlanet, updatePlanet } from '@/api/admin/t-planet'
import { listTProject } from '@/api/admin/project'
import { listTEnv } from '@/api/admin/env'

export default {
  name: 'Planet',
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      planetList: [],
      // 项目列表
      projectList: [],
      // env list
      envList: [],
      // 环境字典列表
      envWordList: [],
      // 类型数据字典
      statusOptions: [],
      // 是否海外字典
      inlandOptions: [],
      // 关系表类型
      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        name: undefined
      },
      // 表单参数
      form: {
      },
      // 表单校验
      rules: { name: [{ required: true, message: '不能为空', trigger: 'blur' }],
        planetId: [{ required: true, message: '不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.geProjectList()
    // this.getEnvList()
    this.getList()

    this.getDicts('env_type').then(response => {
      this.envWordList = response.data
      // console.log('env_type', response.data)
    })

    this.getDicts('sys_common_status').then(response => {
      this.statusOptions = response.data
      console.log(response.data)
    })

    this.getDicts('is_inland').then(response => {
      this.inlandOptions = response.data
      console.log(response.data)
    })
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      listPlanet(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.planetList = response.data.list
        console.log(this.planetList)
        this.total = response.data.count
        this.loading = false
      }
      )
    },
    geProjectList() {
      listTProject(this.addDateRange({
        pageIndex: 1,
        pageSize: 999
      }, this.dateRange)).then(response => {
        this.projectList = response.data.list
        // console.log('projectList', this.projectList)
        // this.total = response.data.count
      }
      )
    },
    getEnvList() {
      listTEnv(this.addDateRange({
        pageIndex: 1,
        pageSize: 999
      }, this.dateRange)).then(response => {
        this.envList = response.data.list
        console.log('env', this.envList)
        // this.total = response.data.count
      }
      )
    },
    envProductName(row) {
      let pName = '-'
      for (const key in this.projectList) {
        if (this.projectList[key].product_id === row.productId) {
          pName = this.projectList[key].name + '(' + this.projectList[key].product_id + ')'
          break
        }
      }
      return pName
    },
    inlandContent(row) {
      let envName = '-'
      for (const key in this.inlandOptions) {
        if (parseInt(this.inlandOptions[key].value) === row.isInland) {
          envName = this.inlandOptions[key].label
          break
        }
      }
      return envName
    },
    envWordShow(row) {
      let envName = ''
      for (let index = 0; index < this.envWordList.length; index++) {
        const keyRow = this.envWordList[index]
        var envId = parseInt(keyRow.value)
        if (envId === row.envId) {
          envName = keyRow.label
          break
        }
      }
      return envName
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {

        id: undefined,
        product_id: undefined,
        planet_id: undefined,
        name: undefined,
        aliasName: undefined,
        relationName: undefined,
        describe: undefined,
        isInland: undefined,
        createUser: undefined,
        updateUser: undefined,
        status: undefined,
        app_id: undefined,
        app_security: undefined
      }
      this.resetForm('form')
    },
    getImgList: function() {
      this.form[this.fileIndex] = this.$refs['fileChoose'].resultList[0].fullUrl
    },
    fileClose: function() {
      this.fileOpen = false
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加渠道'
      this.isEdit = false
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()

      console.log(row)

      const id =
                row.id || this.ids
      getPlanet(id).then(response => {
        console.log(response)
        this.form = response.data
        this.open = true
        this.title = '修改渠道信息'
        this.isEdit = true
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id !== undefined) {
            console.log(this.form)
            updatePlanet(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          } else {
            console.log(this.form)
            addPlanet(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var Ids = (row.id && [row.id]) || this.ids

      this.$confirm('是否确认删除编号为"' + Ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delPlanet({ 'ids': Ids })
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function() {
      })
    }
  }
}
</script>
