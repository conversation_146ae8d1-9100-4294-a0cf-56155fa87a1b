<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="项目" prop="productId">
            <el-select
              v-model="queryParams.productId"
              placeholder="请选项目"
              size="small"
              @change="changeSelectQuery"
            >
              <el-option
                v-for="dict in projectList"
                :key="parseInt(dict.id)"
                :label="dict.name"
                :value="parseInt(dict.product_id)"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="渠道" prop="planetId">
            <el-select
              v-model="queryParams.planetId"
              placeholder="请选渠道"
              clearable
              size="small"
            >
              <el-option
                v-for="dict in planetSelectQueryList"
                :key="parseInt(dict.id)"
                :label="dict.name"
                :value="parseInt(dict.planetId)"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <el-button
              v-permisaction="['admin:env:add']"
              type="primary"
              icon="el-icon-plus"
              size="mini"
              plain
              @click="handleAdd"
            >新增
            </el-button>
          </el-form-item>
        </el-form>

        <el-table v-loading="loading" :data="envList" @selection-change="handleSelectionChange">
          <el-table-column
            label="ID"
            align="center"
            prop="id"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="项目"
            align="left"
            prop="productId"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              {{ envProductName(scope.row) }}
            </template>
          </el-table-column>
          <el-table-column
            label="Planet"
            align="center"
            prop="pid"
          >
            <template slot-scope="scope">
              <el-tag>{{ pidContent(scope.row) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="部署环境"
            align="center"
            prop="name"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <el-tag>{{ envWordShow(scope.row) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="中文名"
            align="center"
            prop="zhName"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="创建时间" align="center" prop="createdAt" width="200">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createdAt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="更新时间" align="center" prop="createdAt" width="200">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.updatedAt) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要修改吗?"
                confirm-button-text="修改"
                @onConfirm="handleUpdate(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['admin:env:edit']"
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                >修改
                </el-button>sys-role
              </el-popconfirm>
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要删除吗?"
                confirm-button-text="删除"
                @onConfirm="handleDelete(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['admin:env:remove']"
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                >删除
                </el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="950px">
          <el-form ref="form" :model="form" :rules="rules" label-width="150px">
            <el-form-item label="项目" prop="productId">
              <el-select
                v-model="form.productId"
                placeholder="请选择项目"
                @change="changeSelect"
              >
                <el-option
                  v-for="dict in projectList"
                  :key="parseInt(dict.id)"
                  :label="dict.name"
                  :value="parseInt(dict.product_id)"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="渠道" prop="planetId">
              <el-select
                v-model="form.planetId"
                placeholder="请选择Planet"
              >
                <el-option
                  v-for="dict in planetSelectList"
                  :key="parseInt(dict.id)"
                  :label="dict.name"
                  :value="parseInt(dict.planetId)"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="EnvType" prop="envType">
              <el-select
                v-model="form.envType"
                placeholder="选择部署EnvType"
                size="small"
              >
                <el-option
                  v-for="dict in envWordList"
                  :key="parseInt(dict.value)"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="环境名称" prop="name">
              <el-input
                v-model="form.name"
                placeholder="环境名称（dev,test,pre,prod）"
              />
            </el-form-item>
            <el-form-item label="中文名称" prop="zhName">
              <el-input
                v-model="form.zhName"
                placeholder="中文名称"
              />
            </el-form-item>
            <el-form-item label="区时" prop="timezone">
              <el-input
                v-model="form.timezone"
                placeholder="区时"
              />
            </el-form-item>
            <el-form-item label="云服务" prop="cloud">
              <el-input
                v-model="form.cloud"
                placeholder="云服务"
              />
            </el-form-item>
            <el-form-item label="Host" prop="host">
              <el-input
                v-model="form.host"
                placeholder="Host"
              />
            </el-form-item>
            <el-form-item label="IP" prop="ip">
              <el-input
                v-model="form.ip"
                placeholder="IP"
              />
            </el-form-item>
            <el-form-item label="Port" prop="port">
              <el-input
                v-model="form.port"
                type="number"
                placeholder="Port"
              />
            </el-form-item>
            <el-form-item label="GmAddr" prop="gmAddr">
              <el-input
                v-model="form.gmAddr"
                placeholder="GM服务器地址"
              />
            </el-form-item>
            <el-form-item label="ClickHouse地址" prop="ckAddr">
              <el-input
                v-model="form.ckAddr"
                placeholder="ClickHouse地址"
              />
            </el-form-item>
            <el-form-item label="配置OSS" prop="configOssAddr">
              <el-input
                v-model="form.configOssAddr"
                placeholder="配置上传OSS地址，按=FTP:addr|username|password配置|baseDir, OSS则baseDIR为Bucket"
              />
            </el-form-item>
            <el-form-item label="consul地址" prop="consulAddr">
              <el-input
                v-model="form.consulAddr"
                placeholder="发现注册地址"
              />
            </el-form-item>
            <el-form-item label="游戏库地址" prop="gameSqlAddr">
              <el-input
                v-model="form.gameSqlAddr"
                placeholder="game_sql_addr"
              />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
            <el-button type="warning" plain @click="submitCopyForm">复 制</el-button>
          </div>
        </el-dialog>
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { addTEnv, delTEnv, getTEnv, listTEnv, updateTEnv } from '@/api/admin/env'
import { listTProject } from '@/api/admin/project'
import { listPlanet } from '@/api/admin/t-planet'

export default {
  name: 'TEnv',
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      envList: [],
      // 项目列表
      projectList: [],
      // Planet列表
      planetList: [],
      // 级联菜单选择临时选项
      planetSelectList: [],
      planetSelectQueryList: [],
      // 环境字典列表
      envWordList: [],

      // 关系表类型

      // 查询参数
      queryParams: {
        pid: undefined,
        pageIndex: 1,
        pageSize: 10

      },
      // 表单参数
      form: {
      },
      // 表单校验
      rules: {
        name: [
          { required: true, message: '名称不能为空', trigger: 'blur' }
        ],
        envId: [
          { required: true, message: '环境id不能为空', trigger: 'blur' }
        ],
        pid: [
          { required: true, message: '项目不能为空', trigger: 'blur' }
        ],
        zhName: [
          { required: true, message: '中文名称不能为空', trigger: 'blur' }
        ],
        planetId: [
          { required: true, message: '请选择Planet', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.geProjectList()
    this.getPlanetList()
    this.getList()

    this.getDicts('env_type').then(response => {
      this.envWordList = response.data
      // console.log('env_type', response.data)
    })
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      listTEnv(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.envList = response.data.list
        // console.log(this.envList)
        this.total = response.data.count
        this.loading = false
      })
    },
    geProjectList() {
      listTProject(this.addDateRange({
        pageIndex: 1,
        pageSize: 999
      }, this.dateRange)).then(response => {
        this.projectList = response.data.list
        // this.total = response.data.count
      }
      )
    },
    getPlanetList() {
      listPlanet(this.addDateRange({
        pageIndex: 1,
        pageSize: 999
      }, this.dateRange)).then(response => {
        this.planetList = response.data.list
        // console.log(this.planetList)
        // this.total = response.data.count
      }
      )
    },
    changeSelect(value) {
      // 联动子集reset
      var tmpSelect = []
      // console.log('planetList', this.planetList)
      this.form.planetId = ''
      for (let index = 0; index < this.planetList.length; index++) {
        const element = this.planetList[index]
        if (parseInt(element.productId) === value) {
          tmpSelect.push(element)
        }
      }
      this.planetSelectList = tmpSelect
    },
    changeSelectQuery(value) {
      // 联动子集reset
      var tmpSelect = []
      this.form.planetId = ''
      for (let index = 0; index < this.planetList.length; index++) {
        const element = this.planetList[index]
        if (parseInt(element.productId) === value) {
          tmpSelect.push(element)
        }
      }
      this.planetSelectQueryList = tmpSelect
    },
    envProductName(row) {
      let pName = '-'
      for (const key in this.projectList) {
        if (parseInt(this.projectList[key].product_id) === row.productId) {
          pName = this.projectList[key].name + '(' + this.projectList[key].product_id + ')'
          break
        }
      }
      return pName
    },
    pidContent(row) {
      let pidName = 'unknown'
      for (const key in this.planetList) {
        var planetId_num = parseInt(this.planetList[key].planetId)
        if (planetId_num === row.planetId) {
          pidName = this.planetList[key].name
          break
        }
      }
      return pidName
    },

    envWordShow(row) {
      let envName = ''
      for (let index = 0; index < this.envWordList.length; index++) {
        const keyRow = this.envWordList[index]
        var envId = parseInt(keyRow.value)
        if (envId === row.envType) {
          envName = keyRow.label
          break
        }
      }
      return envName
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {

        id: undefined,
        planetId: undefined,
        name: undefined,
        zhName: undefined,
        timezone: undefined,
        host: undefined,
        ip: undefined,
        port: undefined,
        gmAddr: undefined,
        ckAddr: undefined,
        zkAddr: undefined,
        sqlAddr: undefined
      }
      this.resetForm('form')
    },
    getImgList: function() {
      this.form[this.fileIndex] = this.$refs['fileChoose'].resultList[0].fullUrl
    },
    fileClose: function() {
      this.fileOpen = false
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加环境'
      this.isEdit = false
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      // const id = row.id
      const id = row.id
      // console.log('handleUpdate row', row)
      getTEnv(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改环境'
        this.isEdit = true
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        // console.log('this.form', this.form)
        this.form.port = parseInt(this.form.port)
        if (valid) {
          if (this.form.id !== undefined) {
            updateTEnv(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          } else {
            addTEnv(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          }
        }
      })
    },
    /** 复制按钮 */
    submitCopyForm: function() {
      this.$refs['form'].validate(valid => {
        this.form.port = parseInt(this.form.port)
        if (valid) {
          addTEnv(this.form).then(response => {
            if (response.code === 200) {
              this.msgSuccess(response.msg)
              this.open = false
              this.getList()
            } else {
              this.msgError(response.msg)
            }
          })
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var Ids = (row.id && [row.id]) || this.ids

      this.$confirm('是否确认删除编号为"' + Ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delTEnv({ 'ids': Ids })
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function() {
      })
    }
  }
}
</script>
