
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card>
        <el-steps :active="3" align-center>
          <el-step title="步骤1" description="在Git编辑配置" />
          <el-step title="步骤2" description="检查配置后Push" />
          <el-step title="步骤3" description="在本页检查并且提交审核" />
          <el-step title="步骤4" description="审核发布配置" />
        </el-steps>
      </el-card>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="终端名" prop="endpointName"><el-input
            v-model="queryParams.endpointName"
            placeholder="请输入终端名"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="文件名" prop="fileName"><el-input
            v-model="queryParams.fileName"
            placeholder="请输入文件名"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="Sheet名" prop="sheetName"><el-input
            v-model="queryParams.sheetName"
            placeholder="请输入Sheet名"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="选状态"
              size="mini"
              style="width: 160px"
              @change="getStatusChange"
            >
              <el-option
                v-for="dict in checkTypeOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <el-button v-if="showBtn1" type="warning" icon="el-icon-circle-check" @click="openGridPanel">提 交</el-button>
          </el-form-item>
        </el-form>
        <el-table v-loading="loading" :data="tConfigOperateMasterList" whi @selection-change="handleSelectionChange">
          <el-table-column label="流水号" align="left" prop="serialNo" width="180" :show-overflow-tooltip="true" />
          <el-table-column label="文件名" align="left" prop="fileName" width="200" :show-overflow-tooltip="true" />
          <el-table-column label="Sheet名" align="left" prop="sheetName" width="200" :show-overflow-tooltip="true" />
          <el-table-column label="对比线上" align="left" width="80">
            <template slot-scope="scope">
              <span v-if="scope.row.change==1"><el-tag type="warning">有差异</el-tag></span>
              <span v-if="scope.row.change==0"><el-tag type="primary">一致</el-tag></span>
            </template>
          </el-table-column>
          <el-table-column label="Git用户" align="center" prop="gitName" width="100" :show-overflow-tooltip="true" />
          <el-table-column label="终端" align="center" prop="endpointName" width="240" :show-overflow-tooltip="true" />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                slot="reference"
                v-permisaction="['config:tConfigOperateMaster:edit']"
                size="mini"
                type="text"
                icon="el-icon-view"
                @click="handleView(scope.row)"
              >查看
              </el-button>
              <el-button
                slot="reference"
                v-permisaction="['config:tConfigOperateMaster:edit']"
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleCmp(scope.row)"
              >对比
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="950px">
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">
            <el-row>
              <el-col :span="12"><h4 style="height: 5px;">流水号：{{ form.serialNo }}</h4></el-col>
              <el-col :span="12"><h4 style="height: 5px;">Git用户：{{ form.gitName }}</h4></el-col>
            </el-row>
            <el-row>
              <el-col :span="12"><h4 style="height: 5px;">终端IP：{{ form.clientIp }}</h4></el-col>
              <el-col :span="12"><h4 style="height: 5px;">终端名：{{ form.endpointName }}</h4></el-col>
            </el-row>
            <el-row>
              <el-col :span="12"><h4 style="height: 5px;">文件名：{{ form.fileName }}</h4></el-col>
              <el-col :span="12"><h4 style="height: 5px;">Sheet名：{{ form.sheetName }}</h4></el-col>
            </el-row>
            <el-row>
              <el-col :span="24"><h4 style="height: 5px;">Git日志：{{ form.gitLog }} </h4></el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <h4 style="height: 5px;">内容：</h4>
              </el-col>
              <el-col :span="24">
                <pre>{{ jsonView(form.ctxJson) }}</pre>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <h4 style="height: 5px;"><el-divider content-position="left">操作记录：</el-divider></h4>
              </el-col>
              <el-col :span="24">
                <el-timeline :reverse="reverse">
                  <el-timeline-item
                    v-for="(activity, index) in activities"
                    :key="index"
                    :timestamp="activity.createdAt"
                  >
                    {{ activity.endpointName }}
                    <el-tag v-if="activity.status==0" type="success" effect="plain">最新</el-tag>
                    <el-tag v-if="activity.status==1" type="info" effect="plain">已被覆盖</el-tag>
                  </el-timeline-item>
                </el-timeline>
              </el-col>
            </el-row>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="cancel">知道了</el-button>
          </div>
        </el-dialog>

        <!-- 对比 -->
        <el-dialog :title="title" :visible.sync="openCmp" width="1200px">
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">
            <el-row>
              文件：{{ form.fileName }}
            </el-row>
            <el-row>
              表名：{{ form.sheetName }}
            </el-row>
            <el-row :gutter="20">
              <el-col :span="10">
                <el-divider content-position="center">已发配置</el-divider>
              </el-col>
              <el-col :span="10">
                <el-divider content-position="center">当前配置</el-divider>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="10">编辑者：{{ cmpObj.endpointName }}</el-col>
              <el-col :span="10">编辑者：{{ form.endpointName }}</el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="10">MD5值：{{ cmpObj.md5 }}</el-col>
              <el-col :span="10">MD5值：{{ form.md5 }}</el-col>
            </el-row>
            <el-row>
              <template>
                <div class="compare">
                  <CodeDiff
                    class="center"
                    :old-string="oldStrToCompare"
                    :new-string="newStrToCompare"
                    :draw-file-list="true"
                    :context="3000"
                    :is-show-no-change="true"
                    output-format="side-by-side"
                  />
                </div>
              </template>
            </el-row>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="cancel">知道了</el-button>
          </div>
        </el-dialog>

        <!-- 提交列表 -->
        <el-dialog title="确认提交列表？" :visible.sync="dialogTableVisible" width="1200px">
          <el-table :data="gridData">
            <el-table-column property="fileName" label="文件名" width="180" />
            <el-table-column property="sheetName" label="表名" width="220" />
            <el-table-column property="md5" label="MD5" width="280" />
            <el-table-column property="createdAt" label="日期" width="240" />
            <el-table-column property="endpointName" label="修改人" width="240" />
          </el-table>
          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageIndex"
            :limit.sync="queryParams.pageSize"
            @pagination="openGridPanel"
          />
          <div slot="footer" class="dialog-footer" align="center">
            <el-button @click="cancelList">取 消</el-button>
            <el-button type="primary" @click="handlePub">确 定</el-button>
          </div>
        </el-dialog>

        <!-- 提交 -->
        <el-dialog title="确认提交？" :visible.sync="dialogFormVisible">
          <el-form ref="formPub" :model="formPub" :rules="rules">
            <el-form-item label="备注" :label-width="formLabelWidth" prop="remark">
              <el-input v-model="formPub.remark" autocomplete="off" />
            </el-form-item>
            <el-form-item label="审核人" :label-width="formLabelWidth" prop="auditBy">
              <el-select
                v-model="formPub.auditBy"
                placeholder="请选审核人"
                clearable
                size="small"
              >
                <el-option
                  v-for="dict in userList"
                  :key="parseInt(dict.userId)"
                  :label="dict.nickName"
                  :value="parseInt(dict.userId)"
                />
              </el-select>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogFormVisible = false">取 消</el-button>
            <el-button type="primary" @click="submitAudit">确 定</el-button>
          </div>
        </el-dialog>
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { addTConfigOperateMaster, delTConfigOperateMaster, getTConfigOperateMaster, listTConfigOperateMaster, updateTConfigOperateMaster, commitConfigOperateMaster } from '@/api/config/t-config-operate-master'
import { listUser } from '@/api/admin/sys-user'

import store from '../../../store'
import { listTEnv } from '@/api/admin/t-env'
import CodeDiff from 'vue-code-diff'
import { mapGetters } from 'vuex'
export default {
  name: 'TConfigOperateMaster',
  components: {
    CodeDiff
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      openCmp: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      tConfigOperateMasterList: [],
      gridData: [],

      // 关系表类型
      envTypeOptions: [],
      productIdOptions: [],
      channelIdOptions: [],

      oldStr: [],
      newStr: [],
      cmpObj: {},
      checkTypeOptions: [{ value: 0, label: '待处理' }, { value: 2, label: '待审核' }, { value: 3, label: '已审核' }],
      showBtn1: false,
      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        id: undefined,
        serialNo: undefined,
        clientIp: undefined,
        endpointName: undefined,
        envType: undefined,
        productId: undefined,
        channelId: undefined,
        fileName: undefined,
        sheetName: undefined,
        md5: undefined,
        status: undefined,
        ctxJson: undefined,
        remark: undefined,
        hadModify: undefined
      },
      // 表单参数
      form: {
      },
      // 表单参数 确认发布
      formPub: {
      },
      userList: [],
      checked: false,
      reverse: true,
      activities: [],
      dialogFormVisible: false,
      dialogTableVisible: false,
      formLabelWidth: '120px',
      // 表单校验
      rules: { id: [{ required: true, message: '不能为空', trigger: 'blur' }],
        serialNo: [{ required: true, message: '流水号不能为空', trigger: 'blur' }],
        clientIp: [{ required: true, message: '终端IP不能为空', trigger: 'blur' }],
        endpointName: [{ required: true, message: '终端名不能为空', trigger: 'blur' }],
        envType: [{ required: true, message: '环境不能为空', trigger: 'blur' }],
        productId: [{ required: true, message: '产品ID不能为空', trigger: 'blur' }],
        channelId: [{ required: true, message: '渠道ID不能为空', trigger: 'blur' }],
        fileName: [{ required: true, message: '文件名不能为空', trigger: 'blur' }],
        remark: [{ required: true, message: '提交备注不能为空', trigger: 'blur' }],
        auditBy: [{ required: true, message: '审核人不能为空', trigger: 'blur' }],
        sheetName: [{ required: true, message: 'Sheet名不能为空', trigger: 'blur' }]
      }
    }
  },
  computed: {
    ...mapGetters([
      'userId'
    ]),
    oldStrToCompare() {
      return JSON.stringify(this.oldStr, null, 10) // 重点！ 格式化添加空格
    },
    newStrToCompare() {
      return JSON.stringify(this.newStr, null, 10) // 重点！ 格式化添加空格
    }
  },
  created() {
    // 使用watch来观察project数据
    this.$watch(
      () => this.$store.getters.project,
      (newProject) => {
        // 当project数据变化且不为空时，执行getList
        if (newProject && newProject.length === 3) {
          this.queryParams.status = 0
          this.getList()
        }
      },
      {
        immediate: true // 立即执行一次watch
      }
    )
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      this.queryParams.productId = store.getters.project[0]
      this.queryParams.envType = store.getters.project[1]
      this.queryParams.channelId = store.getters.project[2]

      console.log('this.queryParams:', this.queryParams)
      listTConfigOperateMaster(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.tConfigOperateMasterList = response.data.list
        this.total = response.data.count
        var showBtn = false
        if (this.tConfigOperateMasterList.length > 0) {
          for (let index = 0; index < this.tConfigOperateMasterList.length; index++) {
            const element = this.tConfigOperateMasterList[index]
            if (element.status === 0) {
              showBtn = true
              break
            }
          }
          if (showBtn) {
            this.showBtn1 = true
          }
        } else {
          this.showBtn1 = false
        }
        this.loading = false
      }
      )
    },
    getStatusChange() {
      this.getList()
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.openCmp = false
      this.reset()
    },
    cancelList() {
      this.dialogTableVisible = false
      this.queryParams.pageIndex = 1
      this.queryParams.pageSize = 10
      this.resetPubForm()
    },
    // 表单重置
    reset() {
      this.form = {

        id: undefined,
        serialNo: undefined,
        clientIp: undefined,
        endpointName: undefined,
        envType: undefined,
        productId: undefined,
        channelId: undefined,
        fileName: undefined,
        sheetName: undefined,
        md5: undefined,
        ctxJson: undefined,
        remark: undefined
      }
      this.resetForm('form')
    },
    // formPub reset
    resetPubForm() {
      this.formPub = {}
      this.resetForm('formPub')
    },
    getImgList: function() {
      this.form[this.fileIndex] = this.$refs['fileChoose'].resultList[0].fullUrl
    },
    fileClose: function() {
      this.fileOpen = false
    },
    envTypeFormat(row) {
      return this.selectItemsLabel(this.envTypeOptions, row.envType)
    },
    productIdFormat(row) {
      return this.selectItemsLabel(this.productIdOptions, row.productId)
    },
    channelIdFormat(row) {
      return this.selectItemsLabel(this.channelIdOptions, row.channelId)
    },
    // 关系
    getTEnvItems() {
      this.getItems(listTEnv, undefined).then(res => {
        this.envTypeOptions = this.setItems(res, 'envType', 'name')
      })
    },

    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.queryParams.pageSize = 10
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加TConfigOperateMaster'
      this.isEdit = false
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleView(row) {
      this.reset()
      const id =
                row.id || this.ids
      getTConfigOperateMaster(id).then(response => {
        this.form = response.data
        this.activities = this.form.record
        console.log(this.activities)

        this.open = true
        this.title = '查看待操作区'
        this.isEdit = true
      })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id =
                row.id || this.ids
      getTConfigOperateMaster(id).then(response => {
        this.form = response.data
        this.activities = this.form.record
        console.log(this.activities)

        this.open = true
        this.title = '待操作区'
        this.isEdit = true
      })
    },
    /** 修改按钮操作 */
    handleCmp(row) {
      const id =
                row.id || this.ids
      getTConfigOperateMaster(id).then(response => {
        this.form = response.data
        console.log('handleCmp:', this.form)
        this.openCmp = true
        this.title = '对比发布版本'
        this.isEdit = true

        var releaseVer = this.form.release

        this.newStr = this.jsonView(this.form.ctxJson)
        if (releaseVer != null) {
          this.cmpObj = releaseVer
          this.oldStr = this.jsonView(releaseVer.ctxJson)
        } else {
          this.cmpObj = {}
          this.cmpObj.endpointName = ''
          this.cmpObj.md5 = ''
        }
      })
    },
    handlePub(row) {
      var queryParamsUser = {}
      queryParamsUser.pageIndex = 1
      queryParamsUser.pageSize = 100
      /**  sys_tag 1: 审核标签 */
      queryParamsUser.tag = '1'

      listUser(this.addDateRange(queryParamsUser, this.dateRange)).then(response => {
        this.userList = response.data.list
        console.log('response.data.list', this.userList)
        // this.dialogTableVisible = false
        this.dialogFormVisible = true
      }
      )
    },
    /** 打开提交列表 */
    openGridPanel() {
      this.loading = true
      var queryAuditList = {}
      queryAuditList.productId = store.getters.project[0]
      queryAuditList.envType = store.getters.project[1]
      queryAuditList.channelId = store.getters.project[2]
      queryAuditList.pageIndex = 1
      queryAuditList.pageSize = 10

      // this.queryParams.productId = store.getters.project[0]
      // this.queryParams.envType = store.getters.project[1]
      // this.queryParams.channelId = store.getters.project[2]
      // this.queryParams.pageIndex = 1
      // this.queryParams.pageSize = 10

      listTConfigOperateMaster(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        console.log('gridData', response.data.list)
        this.gridData = response.data.list
        this.loading = false
        this.dialogTableVisible = true
      }
      )
    },
    /** 提交 */
    submitAudit() {
      this.$refs['formPub'].validate(valid => {
        if (!valid) return
        this.loading = true
        this.queryParams.productId = store.getters.project[0]
        this.queryParams.envType = store.getters.project[1]
        this.queryParams.channelId = store.getters.project[2]
        this.queryParams.auditBy = this.formPub.auditBy
        this.queryParams.remark = this.formPub.remark

        commitConfigOperateMaster(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
          this.gridData = response.data.list
          this.loading = false
          this.dialogTableVisible = false
          this.dialogFormVisible = false
          this.queryParams.auditBy = 0
          this.queryParams.remark = ''
          this.getList()
        }
        )
      })

      // this.loading = true
      // this.queryParams.productId = store.getters.project[0]
      // this.queryParams.envType = store.getters.project[1]
      // this.queryParams.channelId = store.getters.project[2]
      // this.queryParams.auditBy = this.form.auditBy
      // // this.queryParams.pageIndex = 1
      // // this.queryParams.pageSize = 1000

      // console.log('this.this.form.auditBy', this.queryParams)

      // commitConfigOperateMaster(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
      //   this.gridData = response.data.list
      //   this.loading = false
      //   this.dialogTableVisible = false
      //   this.dialogFormVisible = false
      //   this.getList()
      // }
      // )
    },
    /** Json格式化 */
    jsonView(str) {
      if (str == null || str.length === 0) {
        return ''
      } else {
        return JSON.parse(str)
      }
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id !== undefined) {
            updateTConfigOperateMaster(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          } else {
            addTConfigOperateMaster(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var Ids = (row.id && [row.id]) || this.ids

      this.$confirm('是否确认删除编号为"' + Ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delTConfigOperateMaster({ 'ids': Ids })
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function() {
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.center {
  max-height: 700px;
  max-width: 1200px;
  overflow-y: auto;
  overflow-x: auto;
  overflow-x: auto;
}
/* 样式穿透-起始行左右对齐，*/
.center>>>.d2h-code-side-line{
  height:15px;
}
.center>>>code.hljs{
  padding: 20px;
}
</style>
