
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="检查" prop="checkName"><el-input
            v-model="queryParams.checkName"
            placeholder="请输入check name"
            clearable
            size="mini"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="类型" prop="checkType">
            <el-select
              v-model="queryParams.checkType"
              placeholder="类型"
              clearable
              size="mini"
              style="width: 160px"
            >
              <el-option
                v-for="dict in checkTypeOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <el-button
              v-permisaction="['config:configCheckMaster:add']"
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
            >新增
            </el-button>
          </el-form-item>
        </el-form>

        <el-table v-loading="loading" :data="configCheckMasterList" @selection-change="handleSelectionChange">
          <!-- <el-table-column type="selection" width="55" align="center" /> -->
          <el-table-column type="index" label="序号" width="125" align="center" />
          <el-table-column
            label="检查项"
            align="left"
            prop="checkName"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="检查类型"
            prop="checkType"
            align="left"
            :formatter="statusFormat"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              {{ statusFormat(scope.row) }}
            </template> </el-table-column>
          <el-table-column
            label="检查属性Json"
            align="left"
            prop="paramJson"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要修改吗?"
                confirm-button-text="修改"
                @onConfirm="handleUpdate(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['config:configCheckMaster:edit']"
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                >修改
                </el-button>
              </el-popconfirm>
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要删除吗?"
                confirm-button-text="删除"
                @onConfirm="handleDelete(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['config:configCheckMaster:remove']"
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                >删除
                </el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px">
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">
            <el-form-item label="名称" prop="checkName">
              <el-input
                v-model="form.checkName"
                placeholder="check name"
              />
            </el-form-item>
            <el-form-item label="检查类型" prop="checkType">
              <el-select
                v-model="form.checkType"
                placeholder="检查类型"
                clearable
                size="small"
                style="width: 160px"
              >
                <el-option
                  v-for="dict in checkTypeOptions"
                  :key="parseInt(dict.value)"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="检查属性" prop="paramJson">
              <el-input
                v-model="form.paramJson"
                type="textarea"
                :rows="6"
                placeholder="检查属性Json"
              />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <div>
              <el-tooltip class="item" effect="light" placement="right-start" ali>
                <div>
                  <i class="el-icon-question" />
                </div>
                <div slot="content" align="left">配置说明 <br>
                  1. 范围检查<br>
                  * max：最大值 min：最小值 <br>
                  * style：类型（1：区间内 2：指定值 3:字符数组内） <br>
                  * in：指定数组内 （style为2时有效） <br>
                  * strings ：字符串数组 （style为3时有效）<br>
                  eg:<br>
                  // 指定值为0或1<br>
                  {"In":[0,1],"style":2}<br>
                  <br>
                  // 值为区间范围内<br>
                  {"max":1,"min":9999,"style":1}<br>
                  <br>
                  // 指定值为数组内值 <br>
                  {"In":["",1],"style":2}<br>
                  <br>
                  2. 时间检查<br>
                  * style   : 1 from小于to检查  2 区间不重复检查<br>
                  * fromKey ：区间开始值<br>
                  * toKey   ：区间结束值<br>
                  {"style":1,"fromKey":"StartTime","toKey":"EndTime"}<br>
                  <br>
                  3. 外键检查<br>
                  * sheet ：sheet名<br>
                  * field ： 字段<br>
                  {"sheet":"PointRule","field":"PointRuleID"}<br>
                  <br>
                </div>
              </el-tooltip>
              <el-button type="primary" @click="submitForm">确 定</el-button>
              <el-button @click="cancel">取 消</el-button>
            </div>
          </div>
        </el-dialog>
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { addConfigCheckMaster, delConfigCheckMaster, getConfigCheckMaster, listConfigCheckMaster, updateConfigCheckMaster } from '@/api/config/config-check-master'
import store from '../../../store'

export default {
  name: 'ConfigCheckMaster',
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      configCheckMasterList: [],

      // 关系表类型

      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        planetId: undefined,
        checkName: undefined,
        checkType: undefined

      },
      // 类型数据字典
      checkTypeOptions: [],
      // 表单参数
      form: {
      },
      // 表单校验
      rules: { checkName: [{ required: true, message: 'check name不能为空', trigger: 'blur' }],
        checkType: [{ required: true, message: 'check类型不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()

    this.getDicts('config_check_type').then(response => {
      this.checkTypeOptions = response.data
    })
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      this.queryParams.planetId = store.getters.project[2]
      listConfigCheckMaster(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.configCheckMasterList = response.data.list
        this.total = response.data.count
        this.loading = false
      }
      )
    },
    // 状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.checkTypeOptions, row.checkType)
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {

        id: undefined,
        planetId: undefined,
        checkName: undefined,
        checkType: undefined,
        paramJson: undefined
      }
      this.resetForm('form')
    },
    getImgList: function() {
      this.form[this.fileIndex] = this.$refs['fileChoose'].resultList[0].fullUrl
    },
    fileClose: function() {
      this.fileOpen = false
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加检查项'
      this.isEdit = false
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id =
              row.id || this.ids
      getConfigCheckMaster(id).then(response => {
        this.form = response.data
        var paraJsonStr = this.jsonView(response.data.paramJson)
        this.form.paramJson = JSON.stringify(paraJsonStr, null, 4)
        this.open = true
        this.title = '修改检查项'
        this.isEdit = true
      })
    },
    /** Json格式化 */
    jsonView(str) {
      if (str == null || str.length === 0) {
        return ''
      } else {
        return JSON.parse(str)
      }
    },
    /** 提交按钮 */
    submitForm: function() {
      this.form.planetId = store.getters.project[2]
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id !== undefined) {
            updateConfigCheckMaster(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          } else {
            addConfigCheckMaster(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var Ids = (row.id && [row.id]) || this.ids

      this.$confirm('是否确认删除编号为"' + Ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delConfigCheckMaster({ 'ids': Ids })
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function() {
      })
    }
  }
}
</script>
