
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="58px">
          <el-form-item label="Sheet" prop="sheet"><el-input
            v-model="queryParams.sheet"
            placeholder="请输入sheet"
            clearable
            size="mini"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="文件" prop="fileName"><el-input
            v-model="queryParams.sheetName"
            placeholder="请输入文件名"
            clearable
            size="mini"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="字段" prop="field"><el-input
            v-model="queryParams.field"
            placeholder="请输入field"
            clearable
            size="mini"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="字段名" prop="fieldName"><el-input
            v-model="queryParams.fieldName"
            placeholder="请输入field name"
            clearable
            size="mini"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
          <el-form-item>
            <el-tooltip class="item" content="设置Sheet中的列作为外键值，供其它配置使用" effect="light" placement="bottom" ali>
              <el-button
                v-permisaction="['config:configForeignKeyMaster:add']"
                type="primary"
                icon="el-icon-upload2"
                size="small"
                @click="handleImport"
              >Excel导入外键
                <i class="el-icon-question" />
              </el-button>
            </el-tooltip>
          </el-form-item>
          <el-form-item>
            <el-tooltip class="item" content="导入Enum文件" effect="light" placement="bottom" ali>
              <el-button
                v-permisaction="['config:configForeignKeyMaster:add']"
                type="success"
                icon="el-icon-upload"
                size="small"
                @click="handleImportEnum"
              >Excel导入枚举
                <i class="el-icon-question" />
              </el-button>
            </el-tooltip>
          </el-form-item>
        </el-form>

        <el-table v-loading="loading" :data="configForeignKeyMasterList" @selection-change="handleSelectionChange">
          <!-- <el-table-column type="selection" width="55" align="center" /> -->
          <el-table-column type="index" label="序号" width="55" align="center" />
          <el-table-column
            label="Sheet"
            align="center"
            prop="sheet"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="文件名"
            align="center"
            prop="fileName"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="字段"
            align="center"
            prop="field"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="字段名"
            align="center"
            prop="fieldName"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要修改吗?"
                confirm-button-text="修改"
                @onConfirm="handleUpdate(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['config:configForeignKeyMaster:edit']"
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                >修改
                </el-button>
              </el-popconfirm>
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要删除吗?"
                confirm-button-text="删除"
                @onConfirm="handleDelete(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['config:configForeignKeyMaster:remove']"
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                >删除
                </el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="800px">
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">
            <el-form-item label="sheet" prop="sheet">
              <el-input
                v-model="form.sheet"
                placeholder="sheet"
                :disabled="single"
              />
            </el-form-item>
            <el-form-item label="field" prop="field">
              <el-input
                v-model="form.field"
                placeholder="field"
                :disabled="single"
              />
            </el-form-item>
            <el-form-item label="field名" prop="fieldName">
              <el-input
                v-model="form.fieldName"
                placeholder="field name"
                :disabled="single"
              />
            </el-form-item>
          </el-form>
          <template>
            <div class="compare">
              值对比
              <CodeDiff
                class="center"
                :old-string="oldStrToCompare"
                :new-string="newStrToCompare"
                :draw-file-list="true"
                :context="10"
                :is-show-no-change="true"
                output-format="side-by-side"
              />
            </div>
          </template>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>
        <!-- 文件导入后对话框 -->
        <el-dialog :title="title" :visible.sync="openNomal" width="980px">
          <el-tabs v-model="activeName" @tab-click="handleTab">
            <el-tab-pane
              v-for="tabTitle in configSheetNames"
              :key="tabTitle"
              :label="tabTitle"
              :name="tabTitle"
            />
          </el-tabs>
          <el-table v-loading="loading" :data="configFieldMasterListCompare" :cell-style="cellStyle2" @selection-change="handleSelectionChange">
            <!-- <el-table-column type="selection" width="55" align="center" /> -->
            <el-table-column
              label="字段"
              align="left"
              prop="field"
              :show-overflow-tooltip="true"
            /><el-table-column
              label="字段名"
              align="left"
              prop="fieldName"
              :show-overflow-tooltip="true"
            />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-popconfirm
                  class="delete-popconfirm"
                  title="确认要修改吗?"
                  confirm-button-text="修改规则"
                  @onConfirm="handleImportUpdate(scope.row)"
                >
                  <el-button
                    slot="reference"
                    v-permisaction="['config:configForeignKeyMaster:edit']"
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                  >设为外键值
                  </el-button>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </el-dialog>
        <!-- 枚举文件导入后对话框 -->
        <el-dialog :title="title" :visible.sync="openEnum" width="980px">
          <el-tabs v-model="activeName" @tab-click="handleTab">
            <el-tab-pane
              v-for="tabTitle in configSheetNames"
              :key="tabTitle"
              :label="tabTitle"
              :name="tabTitle"
            />
          </el-tabs>
          <el-table v-loading="loading" :data="configFieldMasterListCompare" :cell-style="cellStyle2" @selection-change="handleSelectionChange">
            <!-- <el-table-column type="selection" width="55" align="center" /> -->
            <el-table-column
              label="字段"
              align="left"
              prop="field"
              :show-overflow-tooltip="true"
            /><el-table-column
              label="字段名"
              align="left"
              prop="paramJsonReq"
              :show-overflow-tooltip="true"
            />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-popconfirm
                  class="delete-popconfirm"
                  title="确认要导入吗?"
                  confirm-button-text="导入"
                  @onConfirm="handleImportEnumUpdate(scope.row)"
                >
                  <el-button
                    slot="reference"
                    v-permisaction="['config:configForeignKeyMaster:edit']"
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                  >导入枚举
                  </el-button>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </el-dialog>
        <!-- 用户导入对话框 文件选择 -->
        <el-dialog :title="uploadFrame.title" :loading="loadingInput" :visible.sync="uploadFrame.open" width="450px">
          <div>
            <div align="center">
              <el-upload
                ref="excel-upload-input"
                action="#"
                class="upload-demo"
                drag
                accept=".xlsx, .xls"
                :on-exceed="handleExceed"
                :http-request="handleClick"
                multiple
                :limit="1"
              >
                <i class="el-icon-upload" />
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                <div slot="tip" class="el-upload__tip">只能上传xls/xlsx文件，且不超过500kb</div>
                <div slot="tip" class="el-upload__tip">导入表中的一列字段作为外键（外键：约定常量值）</div>
              </el-upload>
            </div>
          </div>
        </el-dialog>
        <!-- 用户导入枚举对话框 文件选择 -->
        <el-dialog :title="uploadFrame.title" :visible.sync="uploadFrame.open3" width="450px">
          <div>
            <div align="center">
              <el-upload
                ref="excel-upload-input"
                action="#"
                class="upload-demo"
                drag
                accept=".xlsx, .xls"
                :on-exceed="handleExceed"
                :http-request="handleClick"
                multiple
                :limit="1"
              >
                <i class="el-icon-upload" />
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                <div slot="tip" class="el-upload__tip">只能上传xls/xlsx文件，且不超过500kb</div>
                <div slot="tip" class="el-upload__tip">导入表中的一列字段作为外键（外键：约定常量值）</div>
              </el-upload>
            </div>
          </div>
        </el-dialog>
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { addConfigForeignKeyMaster, delConfigForeignKeyMaster, getConfigForeignKeyMaster, listConfigForeignKeyMaster, updateConfigForeignKeyMaster, addConfigForeignKeyByExcel } from '@/api/config/config-foreign-key-master'
import store from '../../../store'
import XLSX from 'xlsx'
import CodeDiff from 'vue-code-diff'

export default {
  name: 'ConfigForeignKeyMaster',
  components: {
    CodeDiff
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      loadingInput: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false, // 添加修改Frame
      openNomal: false, // 普通文件导入后
      openEnum: false, // 枚举文件导入后面板
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      configForeignKeyMasterList: [],

      // 关系表类型

      // 用户导入参数
      uploadFrame: {
        // 是否显示弹出层（用户导入）
        open: false,
        open3: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        isImportEnum: false
      },

      excelData: {
        planetId: null,
        fileName: null,
        sheet: null,
        header: null,
        results: null
      },
      configAllHeader: {},
      configAllBody: {},
      configFieldMasterList: [],
      configFieldMasterListCompare: {},
      configSheetNames: [], // 导入Excel的所有Sheet名
      selectSheetName: '',
      selectFileName: '',
      activeName: '',
      importFileName: '', // 文件名

      oldStr: [],
      newStr: [],

      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        planetId: undefined,
        sheet: undefined,
        sheetName: undefined,
        field: undefined,
        fieldName: undefined

      },
      // 表单参数
      form: {
      },
      // 表单校验
      rules: { sheet: [{ required: true, message: 'sheet不能为空', trigger: 'blur' }],
        sheetName: [{ required: true, message: 'sheet不能为空', trigger: 'blur' }],
        field: [{ required: true, message: 'field不能为空', trigger: 'blur' }],
        fieldName: [{ required: true, message: 'field name不能为空', trigger: 'blur' }]
      }
    }
  },
  computed: {
    oldStrToCompare() {
      return JSON.stringify(this.oldStr, null, 2) // 重点！ 格式化添加空格
    },
    newStrToCompare() {
      return JSON.stringify(this.newStr, null, 2) // 重点！ 格式化添加空格
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      this.queryParams.planetId = store.getters.project[2]
      listConfigForeignKeyMaster(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.configForeignKeyMasterList = response.data.list
        this.total = response.data.count
        this.loading = false
      }
      )
    },
    // 取消按钮
    cancel() {
      this.open = false
      // this.openEnum = false
      // this.openNomal = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {

        id: undefined,
        planetId: undefined,
        sheet: undefined,
        fileName: undefined,
        field: undefined,
        fieldName: undefined,
        paramJson: undefined
      }
      this.resetForm('form')
    },
    getImgList: function() {
      this.form[this.fileIndex] = this.$refs['fileChoose'].resultList[0].fullUrl
    },
    fileClose: function() {
      this.fileOpen = false
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加ConfigForeignKeyMaster'
      this.isEdit = false
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      // const id =
      //         row.id || this.ids
      const id = row.id
      console.log('row', row)

      if (id === undefined || id === 0) {
        this.form.field = row.field
        this.form.fieldName = row.fieldName
        this.form.sheet = this.selectSheetName
        this.form.fileName = this.selectFileName

        var checkValue = this.getAllValueByField(this.selectSheetName, row.field)

        this.form.paramJson = JSON.stringify(checkValue)
        this.newStr = checkValue
        this.oldStr = []

        this.open = true
        this.title = '设置为外键字段供其它字段使用'
        this.isEdit = false
      } else {
        getConfigForeignKeyMaster(id).then(response => {
          this.form = response.data
          this.open = true
          this.title = '修改ConfigForeignKeyMaster'
          this.isEdit = true
          this.newStr = []
          this.oldStr = JSON.parse(response.data.paramJson)
          // this.form.paramJson = JSON.stringify(checkValue)
        })
      }
    },
    /** 导入厚修改按钮操作 */
    handleImportUpdate(row) {
      this.reset()
      const id = row.id
      console.log('row', row)
      var checkValue = this.getAllValueByField(this.selectSheetName, row.field)

      if (id === undefined || id === 0) {
        this.form.field = row.field
        this.form.fieldName = row.fieldName
        this.form.sheet = this.selectSheetName
        this.form.fileName = this.selectFileName

        this.form.paramJson = JSON.stringify(checkValue)
        this.newStr = checkValue
        this.oldStr = []

        this.open = true
        this.title = '设置为检查外键值集合'
        this.isEdit = false
      } else {
        getConfigForeignKeyMaster(id).then(response => {
          this.form = response.data
          this.open = true
          this.title = '修改外键信息'
          this.isEdit = true
          this.newStr = JSON.parse(JSON.stringify(checkValue))
          this.oldStr = JSON.parse(response.data.paramJson)
          // this.form.paramJson = JSON.stringify(checkValue)
        })
      }
    },
    /** 导入枚举修改按钮操作 */
    handleImportEnumUpdate(row) {
      this.reset()
      const id = row.id
      console.log('handleImportEnumUpdate row', row)
      var rowCtx = row.paramJsonReq
      if (rowCtx.substr(rowCtx.length - 1, 1) === ',') {
        rowCtx = rowCtx.slice(0, rowCtx.length - 1)
      }
      console.log('checkValue rowCtx', rowCtx)

      var checkValue = rowCtx.split(',')

      // console.log('this.form', this.form, this.selectFileName)

      if (id === undefined || id === 0) {
        this.form.field = row.field
        this.form.fieldName = row.field
        this.form.sheet = this.selectSheetName
        this.form.fileName = this.selectFileName

        this.form.paramJson = JSON.stringify(checkValue)
        this.newStr = this.jsonView(this.form.paramJson)
        this.oldStr = []

        this.open = true
        this.title = '设置为检查外键值集合'
        this.isEdit = false
      } else {
        getConfigForeignKeyMaster(id).then(response => {
          this.form = response.data
          this.open = true
          this.title = '修改外键'
          this.isEdit = true
          console.log('response:data:', response.data)
          this.newStr = JSON.parse(JSON.stringify(checkValue))
          this.oldStr = JSON.parse(response.data.paramJson)
          // this.form.paramJson = JSON.stringify(checkValue)
        })
      }
    },
    /** 提交按钮 */
    submitForm: function() {
      this.form.planetId = store.getters.project[2]
      console.log('submitForm:', this.form)

      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id !== undefined) {
            console.log('submitForm newstr', this.newStr)
            this.form.paramJson = JSON.stringify(this.newStr)
            console.log('submitForm:', this.form)
            updateConfigForeignKeyMaster(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }

              if (this.openNomal || this.openEnum) {
                this.changeTab(this.selectSheetName)
              }
            })
          } else {
            this.loading = true
            addConfigForeignKeyMaster(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
                if (this.openNomal || this.openEnum) {
                  this.changeTab(this.selectSheetName)
                }
              } else {
                this.msgError(response.msg)
              }
              this.loading = false
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var Ids = (row.id && [row.id]) || this.ids

      this.$confirm('是否确认删除编号为"' + Ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delConfigForeignKeyMaster({ 'ids': Ids })
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function() {
      })
    },
    /** 导入按钮操作 */
    handleImport() {
      this.uploadFrame.title = '导入配置设置外键'
      this.uploadFrame.open = true
    },
    /** 导入按钮操作 */
    handleImportEnum() {
      this.uploadFrame.title = '导入枚举'
      this.uploadFrame.open3 = true
      this.uploadFrame.isImportEnum = true
    },
    // excel操作
    generateData({ header, results }) {
      const plantID = store.getters.project[2]
      this.excelData.planetId = plantID
      this.excelData.sheet = this.selectSheetName

      if (this.uploadFrame.open3) {
        var readIndex = 0
        var textArr = []
        var tmpObj = {}
        var titles = []
        for (let index = 0; index < results.length; index++) {
          var row = results[index]
          if (row.length === 1) {
            // console.log('title:' + index, row[0])
            readIndex = index
            tmpObj = {}
            tmpObj.field = row[0]
            tmpObj.fieldValue = ''
            textArr.push(tmpObj)
            titles.push(row[0])
          } else {
            if (readIndex + 1 === index) {
              // console.log('context title')
            } else {
              if (results[index][0] !== undefined && results[index][0] !== '') {
                tmpObj.fieldValue = tmpObj.fieldValue + results[index][0] + ','
              }
            }
          }
        }
        console.log('textArr', textArr)
        this.excelData.header = titles
        this.excelData.results = titles
        var paramJsons = []
        for (let ii = 0; ii < textArr.length; ii++) {
          const element = textArr[ii]
          paramJsons.push(element.fieldValue)
        }
        this.excelData.paramJsons = paramJsons
        console.log('generateData:', this.excelData)
        addConfigForeignKeyByExcel(this.excelData).then(response => {
          console.log('response.data-enum', response.data)
          this.openEnum = true
          this.title = '从[' + this.importFileName + ']导入所有的枚举值'
          this.isEdit = false
          this.loading = false
          console.log('enum data', response.data)
          this.configFieldMasterListCompare = response.data
        })
      } else {
        this.excelData.header = header
        this.excelData.results = results[1] // 取出title
        console.log('generateData:', this.excelData)
        console.log('this.excelData.header:', this.excelData.header)
        addConfigForeignKeyByExcel(this.excelData).then(response => {
          console.log(response)
          this.openNomal = true
          this.configFieldMasterListCompare = response.data
          this.title = '从[' + this.importFileName + ']选择一列作为外键'
          this.isEdit = false
          this.loading = false
        // this.msgSuccess(response.msg)
        })
      }
    },
    isExcel(file) {
      return /\.(xlsx|xls|csv)$/.test(file.name)
    },
    handleClick(e) {
      this.loadingInput = true
      // const files = e.target.files
      // const rawFile = files[0] // only use files[0]
      const rawFile = e.file
      console.log('rawFile', rawFile.name)
      this.importFileName = rawFile.name
      if (!rawFile) return
      this.upload(rawFile)
      this.uploadFrame.open = false
      this.$refs['excel-upload-input'].clearFiles()
    },
    handleUpload() {
      this.$refs['excel-upload-input'].click()
    },
    handleDrop(e) {
      e.stopPropagation()
      e.preventDefault()
      if (this.loading) return
      const files = e.dataTransfer.files
      if (files.length !== 1) {
        this.$message.error('Only support uploading one file!')
        return
      }
      const rawFile = files[0] // only use files[0]

      if (!this.isExcel(rawFile)) {
        this.$message.error('Only supports upload .xlsx, .xls, .csv suffix files')
        return false
      }
      this.upload(rawFile)
      e.stopPropagation()
      e.preventDefault()
    },
    handleDragover(e) {
      e.stopPropagation()
      e.preventDefault()
      e.dataTransfer.dropEffect = 'copy'
    },
    handleTab(tab, event) {
      console.log('tab.name', tab.name)
      this.changeTab(tab.name)
    },
    handleExceed(files, fileList) {
      this.$message.warning(`共选择了${files.length}个文件`)
    },
    upload(rawFile) {
      this.$refs['excel-upload-input'].value = null // fix can't select the same excel

      if (!this.beforeUpload) {
        this.readerData(rawFile)
        return
      }

      const before = this.beforeUpload(rawFile)
      if (before) {
        this.readerData(rawFile)
      }
    },
    changeTab(sheetName) {
      // 首先读取第一个Sheet
      this.loading = true
      this.selectSheetName = sheetName
      this.activeName = sheetName
      const header = this.configAllHeader[sheetName]
      const results = this.configAllBody[sheetName]

      console.log('changeTab header', header)
      console.log('changeTab results', results)

      if (results === 'undefined' || results.length === 0) {
        return
      }

      this.generateData({ header, results })
    },
    readerData(rawFile) {
      this.loading = true
      console.log('filename', rawFile.name)
      this.selectFileName = rawFile.name

      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = e => {
          const data = e.target.result
          const workbook = XLSX.read(data, { type: 'array' })
          console.log('resultresultresult', workbook)
          this.configSheetNames = workbook.SheetNames

          for (let index = 0; index < this.configSheetNames.length; index++) {
            const sheetName = workbook.SheetNames[index]
            const aHeadCenum = sheetName.startsWith('c.')

            const worksheet = workbook.Sheets[sheetName]
            // const header = this.getHeaderRow(worksheet, { header: 1, defval: '' })
            const header = this.getHeaderRow(worksheet, { header: 1 })
            const result = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
            if (aHeadCenum) {
              // 转换Const为列
              var newHeader = []
              var newRow = []
              for (let index = 1; index < result.length; index++) { // 过滤const的标题
                const element = result[index]
                newRow[index - 1] = element[0]
                newHeader[index - 1] = element[2]
              }
              var newResult = []
              newResult.push(newHeader)
              newResult.push(newRow)

              this.configAllHeader[sheetName] = newHeader
              this.configAllBody[sheetName] = newResult
            } else {
              this.configAllHeader[sheetName] = header
              this.configAllBody[sheetName] = result
            }
            console.log('this.configAllBody', this.configAllBody)
          }

          // 默认显示第一个sheet
          const firstSheetName = workbook.SheetNames[0]
          this.changeTab(firstSheetName)

          this.loading = false
          this.loadingInput = false
          resolve()
        }
        reader.readAsArrayBuffer(rawFile)
      })
    },
    getHeaderRow(sheet) {
      const headers = []
      const range = XLSX.utils.decode_range(sheet['!ref'])

      // let C
      // const R = range.s.r // 起始行
      const R = 2 // 起始行
      const CStart = range.s.c // Start column
      const CEnd = range.e.c // End column

      for (let C = CStart; C <= CEnd; ++C) {
        const cellRef = XLSX.utils.encode_cell({ c: C, r: R })
        const cell = sheet[cellRef]
        let hdr = '' // Default header value

        if (cell && cell.t) {
          hdr = XLSX.utils.format_cell(cell)
        }

        headers.push(hdr)
      }

      return headers
    },
    // methods中写方法
    cellStyle(data) {
      var paramJson = data.row.paramJson
      if (paramJson !== '') {
        var checkIdArray = JSON.parse(paramJson)
        if (checkIdArray.length === 0) {
          return 'color: gray'
        }
        return 'color: green'
      }

      return 'color: gray'
    },
    // methods中写方法
    cellStyle2(data) {
      var id = data.row.id

      if (id !== 0) {
        var objDb = JSON.parse(data.row.paramJson)
        // console.log('objDb', objDb)

        var rowCtx = data.row.paramJsonReq
        if (rowCtx.substr(rowCtx.length - 1, 1) === ',') {
          rowCtx = rowCtx.slice(0, rowCtx.length - 1)
        }

        var checkValue = rowCtx.split(',')
        // console.log('checkValue', checkValue)

        if (objDb.length !== checkValue.length) {
          return 'color: blue'
        }

        return 'color: green'
      }

      return 'color: gray'
    },
    /** Json格式化 */
    jsonView(str) {
      if (str == null || str.length === 0) {
        return ''
      } else {
        return JSON.parse(str)
      }
    },
    // 获取Sheet field列的所有值
    getAllValueByField(sheetName, fieldName) {
      var titleData = this.configAllBody[sheetName][1]
      var ckIndex = 0
      for (let index = 0; index < titleData.length; index++) {
        var element = titleData[index]
        if (element === fieldName) {
          ckIndex = index
          break
        }
      }

      var values = []
      for (let index = 0; index < this.configAllBody[sheetName].length; index++) {
        if (index <= 1) {
          continue
        }
        var element2 = this.configAllBody[sheetName][index]
        values.push(element2[ckIndex])
      }
      console.log('checkCol', values)
      return values
    }
  }
}
</script>

<style>
  .excel-upload-input{
    border: 1px dashed #2208e9;
    border-radius: 6px;
    border-color: #1409da;
    font-size: 18px;
    align-self: center;
    /* background-color: #bed5e8; */
    width: 280px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}
</style>

<style lang="scss" scoped>
.center {
  max-height: 700px;
  overflow-y: auto;
  overflow-x: auto;
  // overflow-x: auto;
}
/* 样式穿透-起始行左右对齐，*/
.center>>>.d2h-code-side-line{
  height:15px;
}
.center>>>code.hljs{
  padding: 0;
}
</style>
