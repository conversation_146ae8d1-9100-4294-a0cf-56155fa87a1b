
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-cardTitle">
        <el-steps finish-status="success">
          <el-step title="步骤 1 : 导入配置Excel" />
          <el-step title="步骤 2 ：选择字段类型" />
          <el-step title="步骤 3 ：给字段添加规则" />
          <el-step title="步骤 4 ：同步到目标环境" />
        </el-steps>
      </el-card>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="Sheet" prop="field"><el-input
            v-model="queryParams.sheet"
            placeholder="请输入Sheet名"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="字段" prop="field"><el-input
            v-model="queryParams.field"
            placeholder="请输入字段"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="字段名" prop="fieldName"><el-input
            v-model="queryParams.fieldName"
            placeholder="请输入字段名"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              v-permisaction="['config:configFieldMaster:add']"
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-tooltip class="item" content="导入要设置的Sheet，给字段设置检查规则" effect="light" placement="bottom" ali>
              <el-button
                v-permisaction="['config:configFieldMaster:add']"
                type="primary"
                icon="el-icon-upload2"
                size="mini"
                @click="handleImport"
              >Excel批量处理字段
                <i class="el-icon-question" />
              </el-button>
            </el-tooltip>
          </el-col>
        </el-row>

        <el-table v-loading="loading" :data="configFieldMasterList" @selection-change="handleSelectionChange">
          <!-- <el-table-column type="selection" width="55" align="center" /> -->
          <el-table-column type="index" label="序号" width="55" align="center" />
          <el-table-column
            label="Sheet名"
            align="center"
            prop="sheet"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="字段"
            align="center"
            prop="field"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="字段名"
            align="center"
            prop="fieldName"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="字段类型"
            align="center"
            prop="fieldType"
            :formatter="statusFormat"
            :show-overflow-tooltip="true"
          ><template slot-scope="scope">
            {{ statusFormat(scope.row) }}
          </template> </el-table-column>
          <el-table-column
            label="规则ID"
            align="center"
            prop="fieldRuleId"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要修改吗?"
                confirm-button-text="修改"
                @onConfirm="handleUpdate(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['config:configFieldMaster:edit']"
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                >修改
                </el-button>
              </el-popconfirm>
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要删除吗?"
                confirm-button-text="删除"
                @onConfirm="handleDelete(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['config:configFieldMaster:remove']"
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                >删除
                </el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px">
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">
            <el-form-item label="Sheet" prop="sheet">
              <el-input
                v-model="form.sheet"
                placeholder="Sheet"
                :disabled="isEdit"
              />
            </el-form-item>
            <el-form-item label="字段" prop="field">
              <el-input
                v-model="form.field"
                placeholder="字段"
                :disabled="isEdit"
              />
            </el-form-item>
            <el-form-item label="字段名" prop="fieldName">
              <el-input
                v-model="form.fieldName"
                placeholder="字段名"
                :disabled="isEdit"
              />
            </el-form-item>
            <el-form-item label="字段类型" prop="fieldType">
              <el-select
                v-model="form.fieldType"
                placeholder="字段类型"
                clearable
                size="small"
                style="width: 160px"
              >
                <el-option
                  v-for="dict in fieldTypeOptions"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="检查选择">
              <el-tree
                ref="menuTree"
                :data="menuOptions"
                show-checkbox
                node-key="id"
                :empty-text="menuOptionsAlert"
                :default-checked-keys="checkedData"
                style="height:220px;overflow-y:auto;overflow-x:hidden;"
              />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>

        <!-- 文件导入后对话框 -->
        <el-dialog :title="title" :visible.sync="open2" width="980px">
          <el-alert
            title="⚠️给表字段配置规则。规则是一系列Check的集合。"
            type="warning"
            show-icon
          />
          <el-tabs v-model="activeName" @tab-click="handleTab">
            <el-tab-pane
              v-for="tabTitle in configSheetNames"
              :key="tabTitle"
              :label="tabTitle"
              :name="tabTitle"
            />
          </el-tabs>
          <el-table v-loading="loading" :data="configFieldMasterListCompare" :cell-style="cellStyle" @selection-change="handleSelectionChange">
            <el-table-column
              label="字段"
              align="left"
              prop="field"
              :show-overflow-tooltip="true"
            /><el-table-column
              label="字段名"
              align="left"
              prop="fieldName"
              :show-overflow-tooltip="true"
            />
            <!-- <el-table-column
              label="字段类型"
              align="left"
              prop="fieldType"
              :show-overflow-tooltip="true"
            /><el-table-column
              label="规则ID"
              align="left"
              prop="fieldRuleId"
              :show-overflow-tooltip="true"
            /> -->
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-popconfirm
                  class="delete-popconfirm"
                  title="确认要修改吗?"
                  confirm-button-text="修改规则"
                  @onConfirm="handleUpdate(scope.row)"
                >
                  <el-button
                    slot="reference"
                    v-permisaction="['config:configFieldMaster:edit']"
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                  >修改规则
                  </el-button>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
          <div slot="footer" class="dialog-footer">
            <p>配置后需到配置表管理同步到其它环境</p>
            <!-- <el-button type="primary" @click="submitForm">确 定</el-button> -->
            <el-button @click="cancel2">取 消</el-button>
          </div>
        </el-dialog>
      </el-card>
      <!-- 用户导入对话框 -->
      <el-dialog :title="uploadFrame.title" :visible.sync="uploadFrame.open" width="500px">
        <div>
          <div align="center">
            <el-upload
              ref="excel-upload-input"
              action="#"
              class="upload-demo"
              drag
              accept=".xlsx, .xls"
              :on-exceed="handleExceed"
              :http-request="handleClick"
              multiple
              :limit="1"
            >
              <i class="el-icon-upload" />
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              <div slot="tip" class="el-upload__tip">只能上传xls/xlsx文件，且不超过500kb</div>
              <div slot="tip" class="el-upload__tip">根据导入Excel表头设置规则（规则：就是一组Check的集合）</div>
            </el-upload>
          </div>
        </div>
      </el-dialog>
    </template>
  </BasicLayout>
</template>

<script>
import { addConfigFieldByExcel, getConfigFieldMaster, addConfigFieldMaster, delConfigFieldMaster, listConfigFieldMaster, updateConfigFieldMaster } from '@/api/config/config-master'
import { listConfigRuleMaster } from '@/api/config/config-rule-master'

import store from '../../../store'
import XLSX from 'xlsx'

export default {
  name: 'ConfigFieldMaster',
  components: {
  },
  props: {
    beforeUpload: Function, // eslint-disable-line
    onSuccess: Function// eslint-disable-line
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      open2: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      configAllHeader: {},
      configAllBody: {},
      configFieldMasterList: [],
      configFieldMasterListCompare: {},
      configSheetNames: [], // 导入Excel的所有Sheet名
      activeName: '',
      importFileName: '', // 文件名

      // 关系表类型
      ruleTypeOptions: [],

      menuOptions: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      menuOptionsAlert: '加载中，请稍后',
      checkedData: [],

      // 用户导入参数
      uploadFrame: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false
      },
      excelData: {
        planetId: null,
        sheet: null,
        header: null,
        results: null
      },
      selectSheetName: '',

      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        planetId: undefined,
        sheet: undefined,
        field: undefined,
        fieldName: undefined

      },
      // 类型数据字典
      fieldTypeOptions: [],
      // 表单参数
      form: {
      },
      // 表单校验
      rules: { planetId: [{ required: true, message: 'planet不能为空', trigger: 'blur' }],
        field: [{ required: true, message: '字段不能为空', trigger: 'blur' }],
        fieldName: [{ required: true, message: '字段名不能为空', trigger: 'blur' }],
        fieldType: [{ required: true, message: '不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()

    this.notifyTips()

    this.getDicts('config_field_type').then(response => {
      this.fieldTypeOptions = response.data
    })

    this.getDicts('config_check_rule_type').then(response => {
      this.ruleTypeOptions = response.data
    })
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      this.queryParams.planetId = store.getters.project[2]
      listConfigFieldMaster(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.configFieldMasterList = response.data.list
        this.total = response.data.count
        this.loading = false
      }
      )
    },
    // 状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.fieldTypeOptions, row.fieldType)
    },
    notifyTips() {
      const h = this.$createElement
      this.$notify({
        title: '配置检查规则说明',
        message: h('i', { style: 'color: teal' }, '1. 导入Sheet字段添加规则，\n2. 设置过规则字段颜色显示\n3. 可手动添加'),
        type: 'warning'
        // duration: 0
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 取消按钮 导入对话框
    cancel2() {
      this.open2 = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {

        id: undefined,
        planetId: undefined,
        sheet: undefined,
        field: undefined,
        fieldName: undefined,
        fieldType: undefined,
        fieldRuleId: undefined,
        createdBy: undefined,
        updatedBy: undefined
      }
      this.resetForm('form')
    },
    getImgList: function() {
      this.form[this.fileIndex] = this.$refs['fileChoose'].resultList[0].fullUrl
    },
    /** 查询菜单树结构 */
    getMenuTreeselect() {
      this.menuOptions = []
      listConfigRuleMaster(0).then(response => {
        this.menuOptions = this.getMenuDatas(response)
        console.log('menuOptions', this.menuOptions)
      })
    },
    /** 修改时查询菜单树结构 */
    getUpdateMenuTreeselect(nodeArray) {
      this.menuOptions = []
      listConfigRuleMaster(0).then(response => {
        this.checkedData = nodeArray
        this.menuOptions = this.getMenuDatas(response)
        console.log(this.$refs.menuTree)
        this.$nextTick(function() {
          this.$refs.menuTree.setCheckedKeys(nodeArray)
        })
      })
    },
    // 获取menu数据
    getMenuDatas(response) {
      console.log(this.ruleTypeOptions)
      var menus = []
      for (let ii = 0; ii < this.ruleTypeOptions.length; ii++) {
        var row = {}
        const ruleTypeObj = this.ruleTypeOptions[ii]
        row.ruleType = ruleTypeObj.value
        row.label = ruleTypeObj.label
        row.children = []
        menus.push(row)
      }

      // 取出所有的Type子菜单
      for (let index = 0; index < response.data.list.length; index++) {
        const element = response.data.list[index]
        var nodeInfo = {}
        nodeInfo.label = element.ruleName
        nodeInfo.id = element.id
        for (let rr = 0; rr < menus.length; rr++) {
          const menuRow = menus[rr]
          if (element.ruleType === menuRow.ruleType) {
            menuRow.children.push(nodeInfo)
            continue
          }
        }
      }
      menus.sort(function(a, b) {
        return a.ruleType * 1 - b.ruleType * 1
      })

      return menus
    },
    // 所有菜单节点数据
    getMenuAllCheckedKeys() {
      // 目前被选中的菜单节点
      const checkedKeys = this.$refs.menuTree.getCheckedKeys()
      console.log('目前被选中的菜单节点', checkedKeys)

      return JSON.stringify(checkedKeys)
    },
    fileClose: function() {
      this.fileOpen = false
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '给字段添加规则'
      this.isEdit = false
      this.getMenuTreeselect()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()

      const id = row.id
      console.log('row', row, this.selectSheetName)

      if (id === undefined || id === 0) {
        this.getMenuTreeselect()
        this.form.sheet = this.selectSheetName
        this.form.field = row.field
        this.form.fieldName = row.fieldName

        this.open = true
        this.title = '新增字段检查规则'
        this.isEdit = true
      } else {
        getConfigFieldMaster(id).then(response => {
          this.form = response.data
          this.open = true
          this.title = '修改字段检查规则'
          this.isEdit = true
          var fieldRuleIds = response.data.fieldRuleId
          var fieldRuleArray = JSON.parse(fieldRuleIds)
          console.log('fieldRuleArray', fieldRuleArray)
          this.getUpdateMenuTreeselect(fieldRuleArray)
        })
      }
    },
    /** 提交按钮 */
    submitForm: function() {
      this.form.planetId = store.getters.project[2]
      var allKeys = this.getMenuAllCheckedKeys()
      console.log('allKeys', allKeys)
      this.form.fieldRuleId = allKeys
      // this.form.sheet = this.selectSheetName
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id !== undefined) {
            updateConfigFieldMaster(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
              // 如果是导入界面修改的规则，则刷新下界面
              if (this.open2) {
                this.changeTab(this.selectSheetName)
              }
            })
          } else {
            console.log(this.form)
            addConfigFieldMaster(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var Ids = (row.id && [row.id]) || this.ids

      this.$confirm('是否确认删除编号为"' + Ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delConfigFieldMaster({ 'ids': Ids })
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function() {
      })
    },
    /** 导入按钮操作 */
    handleImport() {
      this.uploadFrame.title = '配置表导入'
      this.uploadFrame.open = true
    },
    handleExceed(files, fileList) {
      this.$message.warning(`共选择了${files.length}个文件`)
    },
    // excel操作
    generateData({ header, results }) {
      this.excelData.header = header
      this.excelData.results = results[1] // 取出title

      console.log('generateData:', this.excelData)
      console.log(this.excelData.results[1])
      // this.onSuccess && this.onSuccess(this.excelData)

      const plantID = store.getters.project[2]
      this.excelData.planetId = plantID
      this.excelData.sheet = this.selectSheetName

      addConfigFieldByExcel(this.excelData).then(response => {
        console.log(response)
        this.open2 = true
        // this.form2 = response.data
        this.configFieldMasterListCompare = response.data
        this.title = '配置文件：' + this.importFileName
        this.isEdit = false
        this.loading = false
        // this.msgSuccess(response.msg)
      })
    },
    handleDrop(e) {
      e.stopPropagation()
      e.preventDefault()
      if (this.loading) return
      const files = e.dataTransfer.files
      if (files.length !== 1) {
        this.$message.error('Only support uploading one file!')
        return
      }
      const rawFile = files[0] // only use files[0]

      if (!this.isExcel(rawFile)) {
        this.$message.error('Only supports upload .xlsx, .xls, .csv suffix files')
        return false
      }
      this.upload(rawFile)
      e.stopPropagation()
      e.preventDefault()
    },
    handleDragover(e) {
      e.stopPropagation()
      e.preventDefault()
      e.dataTransfer.dropEffect = 'copy'
    },
    handleUpload() {
      this.$refs['excel-upload-input'].click()
    },
    handleClick(e) {
      // const files = e.target.files
      // const rawFile = files[0] // only use files[0]
      this.loading = true
      const rawFile = e.file // only use files[0]
      console.log('rawFile', rawFile.name)
      this.importFileName = rawFile.name
      if (!rawFile) return
      this.upload(rawFile)
      this.uploadFrame.open = false
      this.$refs['excel-upload-input'].clearFiles()
    },
    handleTab(tab, event) {
      console.log('tab.name', tab.name)
      this.changeTab(tab.name)
    },
    upload(rawFile) {
      this.$refs['excel-upload-input'].value = null // fix can't select the same excel

      if (!this.beforeUpload) {
        this.readerData(rawFile)
        return
      }

      const before = this.beforeUpload(rawFile)
      if (before) {
        this.readerData(rawFile)
      }
    },
    changeTab(sheetName) {
      // 首先读取第一个Sheet
      this.loading = true
      this.selectSheetName = sheetName
      this.activeName = sheetName
      // const worksheet = workbook.Sheets[sheetName]
      const header = this.configAllHeader[sheetName]
      const results = this.configAllBody[sheetName]

      console.log('changeTab header', header)
      console.log('changeTab results', results)

      if (results === 'undefined' || results.length === 0) {
        return
      }

      this.generateData({ header, results })
    },
    readerData(rawFile) {
      this.loading = true
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = e => {
          const data = e.target.result
          const workbook = XLSX.read(data, { type: 'array' })
          this.configSheetNames = workbook.SheetNames

          for (let index = 0; index < this.configSheetNames.length; index++) {
            const sheetName = workbook.SheetNames[index]
            const aHeadCenum = sheetName.startsWith('c.')

            const worksheet = workbook.Sheets[sheetName]
            // const header = this.getHeaderRow(worksheet, { header: 1, defval: '' })
            const header = this.getHeaderRow(worksheet, { header: 1 })
            const result = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
            if (aHeadCenum) {
              // 转换Const为列
              var newHeader = []
              var newRow = []
              for (let index = 1; index < result.length; index++) { // 过滤const的标题
                const element = result[index]
                newRow[index - 1] = element[0]
                newHeader[index - 1] = element[2]
              }
              var newResult = []
              newResult.push(newHeader)
              newResult.push(newRow)

              this.configAllHeader[sheetName] = newHeader
              this.configAllBody[sheetName] = newResult
            } else {
              this.configAllHeader[sheetName] = header
              this.configAllBody[sheetName] = result
            }
            console.log('this.configAllBody', this.configAllBody)
          }

          // 默认显示第一个sheet
          const firstSheetName = workbook.SheetNames[0]
          this.changeTab(firstSheetName)

          this.loading = false

          this.loading = false
          resolve()
        }
        reader.readAsArrayBuffer(rawFile)
      })
    },
    getHeaderRow(sheet) {
      const headers = []
      const range = XLSX.utils.decode_range(sheet['!ref'])
      let C
      const R = range.s.r
      /* start in the first row */
      for (C = range.s.r; C <= range.e.c; ++C) { /* walk every column in the range */
        const cell = sheet[XLSX.utils.encode_cell({ c: C, r: R })]
        /* find the cell in the first row */
        // let hdr = 'UNKNOWN ' + C // <-- replace with your desired default
        let hdr = '' // <-- empty default
        if (cell && cell.t) hdr = XLSX.utils.format_cell(cell)
        headers.push(hdr)
      }
      return headers
    },
    isExcel(file) {
      return /\.(xlsx|xls|csv)$/.test(file.name)
    },
    // methods中写方法
    cellStyle(data) {
      var fieldRuleId = data.row.fieldRuleId
      if (fieldRuleId !== '') {
        var checkIdArray = JSON.parse(fieldRuleId)
        if (checkIdArray.length === 0) {
          return 'color: gray'
        }
        return 'color: green'
      }

      return 'color: gray'
    }
  }
}
</script>

<style>
  .excel-upload-input{
    border: 1px dashed #2208e9;
    border-radius: 6px;
    border-color: #1409da;
    font-size: 18px;
    align-self: center;
    /* background-color: #bed5e8; */
    width: 280px;
    height: 45px;
    line-height: 45px;
    text-align: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}
</style>

<style lang="scss" scoped>
.center {
  max-height: 700px;
  overflow-y: auto;
  overflow-x: auto;
  // overflow-x: auto;
}
/* 样式穿透-起始行左右对齐，*/
.center>>>.d2h-code-side-line{
  height:15px;
}
.center>>>code.hljs{
  padding: 0;
}
</style>
