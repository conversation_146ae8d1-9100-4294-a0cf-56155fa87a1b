
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card>
        <el-steps :active="4" align-center>
          <el-step title="步骤1" description="在Git编辑配置" />
          <el-step title="步骤2" description="检查配置后Push" />
          <el-step title="步骤3" description="在本页检查并且提交审核" />
          <el-step title="步骤4" description="审核发布配置" />
        </el-steps>
      </el-card>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="文件名" prop="fileName"><el-input
            v-model="queryParams.fileName"
            placeholder="请输入文件名"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="Sheet名" prop="sheetName"><el-input
            v-model="queryParams.sheetName"
            placeholder="请输入Sheet名"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="选状态"
              size="mini"
              style="width: 160px"
              @change="getStatusChange"
            >
              <el-option
                v-for="dict in checkTypeOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <el-popconfirm
              class="delete-popconfirm"
              title="确认要审批发布吗?"
              confirm-button-text="审批"
              @onConfirm="handleAudit"
            >
              <el-button
                slot="reference"
                size="mini"
                :disabled="showBtn1"
                type="success"
                icon="el-icon-edit"
              >审批发布
              </el-button>
            </el-popconfirm>
            <el-popconfirm
              class="delete-popconfirm"
              title="确认要拒绝审批吗?"
              confirm-button-text="拒绝审批"
              @onConfirm="handleRejectAudit"
            >
              <el-button
                slot="reference"
                size="mini"
                :disabled="showBtn2"
                type="danger"
                icon="el-icon-edit"
              >拒绝审批
              </el-button>
            </el-popconfirm>
          </el-form-item>
        </el-form>

        <el-table v-loading="loading" :data="tConfigAuditMasterList" @selection-change="handleSelectionChange">
          <el-table-column label="流水号" align="center" prop="serialNo" width="220" />
          <el-table-column label="文件名" align="left" prop="fileName" width="150" />
          <el-table-column label="Sheet名" align="left" prop="sheetName" width="150" />
          <el-table-column label="Git用户" align="left" prop="gitName" width="150" />
          <el-table-column label="提交用户" align="left" prop="user_name" width="150" />
          <el-table-column label="状态" align="center" width="80">
            <template slot-scope="scope">
              <span v-if="scope.row.status==2"><el-tag type="success">待审核</el-tag></span>
              <span v-if="scope.row.status==3"><el-tag type="info">已审核</el-tag></span>
              <span v-if="scope.row.status==4"><el-tag type="warning">拒绝</el-tag></span>
            </template>
          </el-table-column>
          <el-table-column label="MD5" align="left" prop="md5" width="320" :show-overflow-tooltip="true" />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                slot="reference"
                size="mini"
                type="text"
                icon="el-icon-view"
                @click="handleUpdate(scope.row)"
              >查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="950px">
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">
            <el-row>
              <el-col :span="12"><h4 style="height: 5px;">流水号：{{ form.serialNo }}</h4></el-col>
              <el-col :span="12"><h4 style="height: 5px;">Git用户：{{ form.gitName }}</h4></el-col>
            </el-row>
            <el-row>
              <el-col :span="12"><h4 style="height: 5px;">终端IP：{{ form.clientIp }}</h4></el-col>
              <el-col :span="12"><h4 style="height: 5px;">终端名：{{ form.endpointName }}</h4></el-col>
            </el-row>
            <el-row>
              <el-col :span="12"><h4 style="height: 5px;">文件名：{{ form.fileName }}</h4></el-col>
              <el-col :span="12"><h4 style="height: 5px;">Sheet名：{{ form.sheetName }}</h4></el-col>
            </el-row>
            <el-row>
              <el-col :span="12"><h4 style="height: 5px;">MD5：{{ form.fileName }}</h4></el-col>
              <!-- <el-col :span="12"><h4 style="height: 5px;">状态：{{ form.status }}</h4></el-col> -->
              <template v-if="form.status === 2">
                <el-col :span="12"><h4 style="height: 5px;">状态：待审核 </h4></el-col>
              </template>
              <template v-if="form.status === 4">
                <el-col :span="12"><h4 style="height: 5px;">状态：废弃 </h4></el-col>
              </template>
            </el-row>
            <el-row>
              <el-col :span="12"><h4 style="height: 5px;">备注：{{ form.remark }}</h4></el-col>
              <el-col :span="12"><h4 style="height: 5px;">提交人：{{ form.submitter }}</h4></el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <h4 style="height: 5px;">内容：</h4>
              </el-col>
              <el-col :span="24">
                <pre>{{ jsonView(form.ctxJson) }}</pre>
              </el-col>
            </el-row>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { addTConfigAuditMaster, delTConfigAuditMaster, getTConfigAuditMaster, listTConfigAuditMaster, updateTConfigAuditMaster } from '@/api/config/t-config-audit-master'
import store from '../../../store'
import { listUser } from '@/api/admin/sys-user'

export default {
  name: 'TConfigAuditMaster',
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      tConfigAuditMasterList: [],
      timeline: [
        { timestamp: '2011-10', content: 'Vue.js 发布 0.1 版本' },
        { timestamp: '2014-02', content: 'Vue.js 1.0 发布' },
        { timestamp: '2016-10', content: 'Vue.js 2.0 发布' },
        { timestamp: '2020-01', content: 'Vue.js 3.0 发布' },
        { timestamp: '2020-02', content: 'Vue.js 3.0 发布' }
      ],
      // 关系表类型
      envTypeOptions: [],
      productIdOptions: [],
      channelIdOptions: [],

      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        id: undefined,
        serialNo: undefined,
        clientIp: undefined,
        endpointName: undefined,
        envType: undefined,
        productId: undefined,
        channelId: undefined,
        fileName: undefined,
        sheetName: undefined,
        md5: undefined,
        ctxJson: undefined,
        status: undefined,
        remark: undefined,
        submitter: undefined,
        auditor: undefined

      },
      // 表单参数
      form: {
      },
      showBtn1: true,
      showBtn2: true,
      checkTypeOptions: [{ value: 2, label: '待审核' }, { value: 3, label: '已审核' }],
      // 表单校验
      rules: { id: [{ required: true, message: '不能为空', trigger: 'blur' }],
        endpointName: [{ required: true, message: '终端名不能为空', trigger: 'blur' }],
        fileName: [{ required: true, message: '文件名不能为空', trigger: 'blur' }],
        sheetName: [{ required: true, message: 'Sheet名不能为空', trigger: 'blur' }],
        status: [{ required: true, message: '状态：0:待发布 1：覆盖 2：待审核 3:已发布 4:废弃不能为空', trigger: 'blur' }],
        submitter: [{ required: true, message: '提交人不能为空', trigger: 'blur' }],
        auditor: [{ required: true, message: '审核人不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    // 使用watch来观察project数据
    this.$watch(
      () => this.$store.getters.project,
      (newProject) => {
        // 当project数据变化且不为空时，执行getList
        if (newProject && newProject.length === 3) {
          this.queryParams.status = 2
          this.getList()
        }
      },
      {
        immediate: true // 立即执行一次watch
      }
    )
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      this.queryParams.productId = store.getters.project[0]
      this.queryParams.envType = store.getters.project[1]
      this.queryParams.channelId = store.getters.project[2]

      listTConfigAuditMaster(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.tConfigAuditMasterList = response.data.list
        this.total = response.data.count
        this.loading = false

        var showBtn = false
        if (this.tConfigAuditMasterList.length > 0) {
          for (let index = 0; index < this.tConfigAuditMasterList.length; index++) {
            const element = this.tConfigAuditMasterList[index]
            if (element.status === 2) {
              showBtn = true
              break
            }
          }
          if (showBtn) {
            this.showBtn1 = false
            this.showBtn2 = false
          }
        } else {
          this.showBtn1 = true
          this.showBtn2 = true
        }
      }
      )
    },
    getStatusChange() {
      this.getList()
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {

        id: undefined,
        serialNo: undefined,
        clientIp: undefined,
        endpointName: undefined,
        fileName: undefined,
        sheetName: undefined,
        md5: undefined,
        ctxJson: undefined,
        status: undefined,
        remark: undefined,
        submitter: undefined,
        auditor: undefined
      }
      this.resetForm('form')
    },
    getImgList: function() {
      this.form[this.fileIndex] = this.$refs['fileChoose'].resultList[0].fullUrl
    },
    fileClose: function() {
      this.fileOpen = false
    },
    envTypeFormat(row) {
      return this.selectItemsLabel(this.envTypeOptions, row.envType)
    },
    productIdFormat(row) {
      return this.selectItemsLabel(this.productIdOptions, row.productId)
    },
    channelIdFormat(row) {
      return this.selectItemsLabel(this.channelIdOptions, row.channelId)
    },
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加TConfigAuditMaster'
      this.isEdit = false
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id =
                row.id || this.ids
      getTConfigAuditMaster(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改TConfigAuditMaster'
        this.isEdit = true
      })
    },
    /** 审批 */
    handleAudit(row) {
      this.form.productId = store.getters.project[0]
      this.form.envType = store.getters.project[1]
      this.form.channelId = store.getters.project[2]
      this.form.status = 3
      console.log(this.form)
      updateTConfigAuditMaster(this.form).then(response => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      })
    },
    /** 拒绝审批 */
    handleRejectAudit(row) {
      this.form.productId = store.getters.project[0]
      this.form.envType = store.getters.project[1]
      this.form.channelId = store.getters.project[2]
      this.form.status = 4
      delTConfigAuditMaster(this.form).then(response => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      })
    },
    /** Json格式化 */
    jsonView(str) {
      if (str === undefined || str === null) {
        return '' // 如果是 undefined 或 null，返回空字符串
      }

      try {
        // 尝试将字符串解析为 JSON 对象
        const obj = JSON.parse(str)
        // 如果成功解析，返回格式化的 JSON 字符串
        return JSON.stringify(obj, null, 2)
      } catch (error) {
        // 如果解析失败，返回原字符串
        console.error('Invalid JSON string:', str, error)
        return str // 或者也可以返回一个错误提示字符串
      }
    },
    handlePub(row) {
      var queryParamsUser = {}
      queryParamsUser.pageIndex = 1
      queryParamsUser.pageSize = 1000

      listUser(this.addDateRange(queryParamsUser, this.dateRange)).then(response => {
        this.userList = response.data.list
        console.log('response.data.list', this.userList)
        this.dialogFormVisible = true
      }
      )
    },
    btnClick(row) {
      this.msgSuccess('暂未实现')
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id !== undefined) {
            updateTConfigAuditMaster(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          } else {
            addTConfigAuditMaster(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var Ids = (row.id && [row.id]) || this.ids

      this.$confirm('是否确认删除编号为"' + Ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delTConfigAuditMaster({ 'ids': Ids })
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function() {
      })
    }
  }
}
</script>
