
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-cardTitle">
        <el-steps finish-status="success">
          <el-step title="步骤 1 : 配置Check" />
          <el-step title="步骤 2 ：添加规则（选择Check项）" />
          <el-step title="步骤 3 ：导入配置并设置规则" />
        </el-steps>
      </el-card>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="规则名称" prop="ruleName"><el-input
            v-model="queryParams.ruleName"
            placeholder="请输入rule name"
            clearable
            size="mini"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="规则类型" prop="ruleType">
            <el-select
              v-model="queryParams.ruleType"
              placeholder="类型"
              clearable
              size="mini"
              style="width: 160px"
            >
              <el-option
                v-for="dict in ruleTypeOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="检查ID" prop="checkId"><el-input
            v-model="queryParams.checkId"
            placeholder="请输入检查ID"
            clearable
            size="mini"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <el-button
              v-permisaction="['config:configRuleMaster:add']"
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
            >新增
            </el-button>
          </el-form-item>
        </el-form>

        <el-table v-loading="loading" :data="configRuleMasterList" @selection-change="handleSelectionChange">
          <!-- <el-table-column type="selection" width="55" align="center" /> -->
          <el-table-column type="index" label="序号" width="55" align="center" />
          <el-table-column
            label="rule name"
            align="left"
            prop="ruleName"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="RuleID"
            align="center"
            prop="id"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="规则类型"
            prop="ruleType"
            align="left"
            :formatter="statusFormat"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              {{ statusFormat(scope.row) }}
            </template> </el-table-column>
          <el-table-column
            label="检查ID"
            align="left"
            prop="checkId"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="操作" align="left" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要修改吗?"
                confirm-button-text="修改"
                @onConfirm="handleUpdate(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['config:configRuleMaster:edit']"
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                >修改
                </el-button>
              </el-popconfirm>
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要删除吗?"
                confirm-button-text="删除"
                @onConfirm="handleDelete(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['config:configRuleMaster:remove']"
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                >删除
                </el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px">
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">

            <el-form-item label="规则名称" prop="ruleName">
              <el-input
                v-model="form.ruleName"
                placeholder="rule name"
              />
            </el-form-item>
            <el-form-item label="规则类型" prop="ruleType">
              <el-select
                v-model="form.ruleType"
                placeholder="规则类型"
                clearable
                size="small"
                style="width: 160px"
              >
                <el-option
                  v-for="dict in ruleTypeOptions"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="检查选择">
              <el-tree
                ref="menuTree"
                :data="menuOptions"
                show-checkbox
                node-key="id"
                :empty-text="menuOptionsAlert"
                :default-checked-keys="checkedData"
                style="height:220px;overflow-y:auto;overflow-x:hidden;"
              />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { addConfigRuleMaster, delConfigRuleMaster, getConfigRuleMaster, listConfigRuleMaster, updateConfigRuleMaster } from '@/api/config/config-rule-master'
import { listConfigCheckMaster } from '@/api/config/config-check-master'
import store from '../../../store'

export default {
  name: 'ConfigRuleMaster',
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      ruleTypeOptions: [],
      configRuleMasterList: [],

      // 检查分类
      checkTypeOptions: [],

      // 关系表类型

      menuOptions: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      menuOptionsAlert: '加载中，请稍后',
      checkedData: [],

      // 查询参数
      queryParams: {
        planetId: undefined,
        pageIndex: 1,
        pageSize: 10,
        ruleName: undefined,
        ruleType: undefined,
        checkId: undefined

      },
      // 表单参数
      form: {
      },
      // 表单校验
      rules: { ruleName: [{ required: true, message: 'rule name不能为空', trigger: 'blur' }],
        ruleType: [{ required: true, message: '规则类型不能为空', trigger: 'blur' }],
        checkId: [{ required: true, message: '检查ID不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()

    this.getDicts('config_check_rule_type').then(response => {
      this.ruleTypeOptions = response.data
    })

    this.getDicts('config_check_type').then(response => {
      this.checkTypeOptions = response.data
    })
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      this.queryParams.planetId = store.getters.project[2]
      listConfigRuleMaster(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.configRuleMasterList = response.data.list
        this.total = response.data.count
        console.log(response.data)
        this.loading = false
      }
      )
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {

        id: undefined,
        planetId: undefined,
        ruleName: undefined,
        ruleType: undefined,
        checkId: undefined
      }
      this.resetForm('form')
    },
    // 状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.ruleTypeOptions, row.ruleType)
    },
    getImgList: function() {
      this.form[this.fileIndex] = this.$refs['fileChoose'].resultList[0].fullUrl
    },
    /** 查询菜单树结构 */
    getMenuTreeselect() {
      this.menuOptions = []
      listConfigCheckMaster(0).then(response => {
        console.log('response', response)
        console.log('this.checkTypeOptions', this.checkTypeOptions)

        this.menuOptions = this.getMenuDatas(response)
      })
    },
    /** 修改时查询菜单树结构 */
    getUpdateMenuTreeselect(nodeArray) {
      this.menuOptions = []
      listConfigCheckMaster(0).then(response => {
        this.checkedData = nodeArray
        this.menuOptions = this.getMenuDatas(response)

        console.log(this.$refs.menuTree)
        this.$nextTick(function() {
          this.$refs.menuTree.setCheckedKeys(nodeArray)
        })
      })
    },
    // 所有菜单节点数据
    getMenuAllCheckedKeys() {
      // 目前被选中的菜单节点
      const checkedKeys = this.$refs.menuTree.getCheckedKeys()
      console.log('目前被选中的菜单节点', checkedKeys)
      const halfCheckedKeys = this.$refs.menuTree.getHalfCheckedKeys()
      console.log('半选中的菜单节点', halfCheckedKeys)

      var ret = []
      for (let ii = 0; ii < checkedKeys.length; ii++) {
        const element = checkedKeys[ii]
        if (element != null) {
          ret.push(element)
        }
      }

      return JSON.stringify(ret)
    },
    // 获取menu数据
    getMenuDatas(response) {
      // 取出所有的CheckType
      var menus = []
      // for (let index = 0; index < response.data.list.length; index++) {
      //   const element = response.data.list[index]
      //   var row = {}
      //   for (let ii = 0; ii < this.checkTypeOptions.length; ii++) {
      //     const eop = this.checkTypeOptions[ii]
      //     var vv = eop.value
      //     var vl = eop.label
      //     var svv = element.checkType + ''
      //     if (vv === svv) {
      //       row.label = vl
      //       break
      //     }
      //   }
      //   row.checkType = element.checkType
      //   row.children = []
      //   menus.push(row)
      // }
      for (let ii = 0; ii < this.checkTypeOptions.length; ii++) {
        var row = {}
        const checkTypeObj = this.checkTypeOptions[ii]
        row.checkType = checkTypeObj.value
        row.label = checkTypeObj.label
        row.children = []
        menus.push(row)
      }

      // 取出所有的Type菜单
      for (let index = 0; index < response.data.list.length; index++) {
        const element = response.data.list[index]
        var nodeInfo = {}
        nodeInfo.label = element.checkName
        nodeInfo.id = element.id
        for (let rr = 0; rr < menus.length; rr++) {
          const menuRow = menus[rr]
          if (element.checkType + '' === menuRow.checkType) {
            console.log(element.checkType + '', menuRow.checkType)
            menuRow.children.push(nodeInfo)
            continue
          }
        }
      }

      return menus
    },
    fileClose: function() {
      this.fileOpen = false
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加规则管理'
      this.isEdit = false
      this.getMenuTreeselect()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id =
              row.id || this.ids
      getConfigRuleMaster(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改规则管理'
        this.isEdit = true
        var checkIds = response.data.checkId
        var checkIdArray = JSON.parse(checkIds)
        console.log('checkIdArray', checkIdArray)
        this.getUpdateMenuTreeselect(checkIdArray)
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.form.planetId = store.getters.project[2]
      var allKeys = this.getMenuAllCheckedKeys()
      console.log('allKeys', allKeys)
      this.form.checkId = allKeys
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id !== undefined) {
            updateConfigRuleMaster(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          } else {
            addConfigRuleMaster(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var Ids = (row.id && [row.id]) || this.ids

      this.$confirm('是否确认删除编号为"' + Ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delConfigRuleMaster({ 'ids': Ids })
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function() {
      })
    }
  }
}
</script>
