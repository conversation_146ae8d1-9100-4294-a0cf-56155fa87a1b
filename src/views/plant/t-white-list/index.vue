
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
          <!-- <el-form-item label="plant" prop="plantId"><el-input
            v-model="queryParams.plantId"
            placeholder="请输入plant"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item> -->
          <el-form-item label="用户ID" prop="uid"><el-input
            v-model="queryParams.uid"
            placeholder="请输入用户ID"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="queryParams.developer" :checked="false">开发者</el-checkbox>
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="queryParams.testerPay" :checked="false">支付</el-checkbox>
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="queryParams.testerGray" :checked="false">灰度白名单</el-checkbox>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              v-permisaction="['plant:tWhiteList:add']"
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
            >新增
            </el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button
              v-permisaction="['plant:tWhiteList:edit']"
              type="success"
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
            >修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-permisaction="['plant:tWhiteList:remove']"
              type="danger"
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
            >删除
            </el-button>
          </el-col> -->
        </el-row>

        <el-table v-loading="loading" :data="tWhiteListList">
          <!-- <el-table-column type="selection" width="55" align="center" /><el-table-column
            label="plant"
            align="center"
            prop="planetId"
            :show-overflow-tooltip="true"
          /> -->
          <el-table-column
            label="用户ID"
            align="center"
            prop="uid"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="开发者"
            align="center"
            prop="developer"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <el-tag v-if="scope.row.developer" type="success"> {{ statusFormat(scope.row.developer) }} </el-tag>
              <el-tag v-if="scope.row.developer==false" type="warning"> {{ statusFormat(scope.row.developer) }} </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="支付权限"
            align="center"
            prop="testerPay"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <el-tag v-if="scope.row.testerPay==true" type="success"> {{ statusFormat(scope.row.testerPay) }} </el-tag>
              <el-tag v-if="scope.row.testerPay==false" type="warning"> {{ statusFormat(scope.row.testerPay) }} </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="灰度权限"
            align="center"
            prop="testerGray"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <el-tag v-if="scope.row.testerGray==true" type="success"> {{ statusFormat(scope.row.testerGray) }} </el-tag>
              <el-tag v-if="scope.row.testerGray==false" type="warning"> {{ statusFormat(scope.row.testerGray) }} </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要修改吗?"
                confirm-button-text="修改"
                @onConfirm="handleUpdate(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['plant:tWhiteList:edit']"
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                >修改
                </el-button>
              </el-popconfirm>
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要删除吗?"
                confirm-button-text="删除"
                @onConfirm="handleDelete(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['plant:tWhiteList:remove']"
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                >删除
                </el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="600px">
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">

            <el-form-item label="planetId" prop="planetId">
              <el-input
                v-model="form.planetId"
                placeholder="planet"
                disabled="disabled"
              />
            </el-form-item>
            <el-form-item label="用户ID" prop="uid">
              <el-input
                v-model="form.uid"
                placeholder="用户ID"
              />
            </el-form-item>
            <el-form-item label="开发者" prop="developer">
              <el-switch
                v-model="form.developer"
              />
            </el-form-item>
            <el-form-item label="支付权限" prop="testerPay">
              <el-switch
                v-model="form.testerPay"
              />
            </el-form-item>
            <el-form-item label="灰度权限" prop="testerGray">
              <el-switch
                v-model="form.testerGray"
              />
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="form.remark"
                placeholder="备注"
              />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { addTWhiteList, delTWhiteList, getTWhiteList, listTWhiteList, updateTWhiteList } from '@/api/plant/whitelist'
import store from '../../../store'

export default {
  name: 'TWhiteList',
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      tWhiteListList: [],

      // 关系表类型

      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        planetId: undefined,
        uid: undefined,
        developer: undefined,
        testerPay: undefined,
        testerGray: undefined

      },
      // 表单参数
      form: {
      },
      // 表单校验
      rules: { planetId: [{ required: true, message: 'plant不能为空', trigger: 'blur' }],
        uid: [{ required: true, message: '用户ID不能为空', trigger: 'blur' }],
        developer: [{ required: true, message: '开发者不能为空', trigger: 'blur' }],
        testerPay: [{ required: true, message: '测试人员_支付不能为空', trigger: 'blur' }],
        testerGray: [{ required: true, message: '测试人员_灰度不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      const plantID = store.getters.project[2]
      this.queryParams.planetId = plantID
      listTWhiteList(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        console.log(response.data.list)
        this.tWhiteListList = response.data.list
        // if (this.tWhiteListList.developer == un) {

        // }
        this.total = response.data.count
        this.loading = false
      }
      )
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {

        id: undefined,
        planetId: undefined,
        uid: undefined,
        developer: undefined,
        testerPay: undefined,
        testerGray: undefined,
        remark: undefined
      }
      this.resetForm('form')
    },
    getImgList: function() {
      this.form[this.fileIndex] = this.$refs['fileChoose'].resultList[0].fullUrl
    },
    fileClose: function() {
      this.fileOpen = false
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      const plantID = store.getters.project[2]
      this.form.planetId = plantID

      this.open = true
      this.title = '添加白名单'
      this.isEdit = false
    },
    statusFormat(yet) {
      if (yet) {
        return '是'
      } else {
        return '否'
      }
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const plantID = store.getters.project[2]
      this.form.planetId = plantID
      const id =
                row.id || this.ids
      getTWhiteList(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改白名单'
        this.isEdit = true
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      console.log(this.$refs['form'])
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id !== undefined) {
            updateTWhiteList(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          } else {
            addTWhiteList(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var Ids = (row.id && [row.id]) || this.ids

      this.$confirm('是否确认删除编号为"' + Ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        console.log(Ids)
        return delTWhiteList({ 'ids': Ids })
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function() {
      })
    }
  }
}
</script>
