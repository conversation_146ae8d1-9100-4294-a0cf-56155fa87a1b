
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="中文名称" prop="chName"><el-input
            v-model="queryParams.chName"
            placeholder="请输入中文名称"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="英文名称" prop="enName"><el-input
            v-model="queryParams.enName"
            placeholder="请输入英文名称"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              v-permisaction="['plant:tFishInfo:add']"
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-permisaction="['plant:tFishInfo:edit']"
              type="success"
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
            >修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-permisaction="['plant:tFishInfo:remove']"
              type="danger"
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
            >删除
            </el-button>
          </el-col>
        </el-row>

        <el-table v-loading="loading" :data="tFishInfoList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" /><el-table-column
            label="中文名称"
            align="center"
            prop="chName"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="英文名称"
            align="center"
            prop="enName"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="规则"
            align="center"
            prop="rule"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="最大KG(LB)"
            align="center"
            prop="weight"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="长度(CM)"
            align="center"
            prop="length"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="价格/KG"
            align="center"
            prop="price"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="鱼饵类型"
            align="center"
            prop="baitType"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="假饵类型"
            align="center"
            prop="lureType"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="生活区域"
            align="center"
            prop="liveArea"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="食物"
            align="center"
            prop="food"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="其他特性"
            align="center"
            prop="feature"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="出没场景"
            align="center"
            prop="scene"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="图片地址"
            align="center"
            prop="imageUrl"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="创建者名称"
            align="center"
            prop="createName"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要修改吗?"
                confirm-button-text="修改"
                @onConfirm="handleUpdate(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['plant:tFishInfo:edit']"
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                >修改
                </el-button>
              </el-popconfirm>
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要删除吗?"
                confirm-button-text="删除"
                @onConfirm="handleDelete(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['plant:tFishInfo:remove']"
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                >删除
                </el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

        <!-- 导入Excel1 -->
        <el-upload
          ref="upload"
          class="upload-demo"
          action=""
          accept=".xlsx, .xls"
          :on-change="exFileChange"
          :multiple="false"
          :auto-upload="false"
        >
          <el-button slot="trigger" type="primary">选取Excel文件</el-button>
          <div slot="tip" class="el-upload__tip">只能上传xls/xlsx文件，且不超过500kb</div>
        </el-upload>

        <!-- 导入Excel2 -->
        <!--
        <el-upload class="upload-demo" drag action="" :on-change="exFileChange"
          :multiple="false" :auto-upload="false">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">只能上传xls/xlsx文件，且不超过500kb</div>
        </el-upload>
        -->

        <!-- 导出数据按钮 -->
        <el-button type="success" @click="submitForm2">导出数据</el-button>
        <el-button type="warning" @click="submitForm3">导入db</el-button>

        <!-- Excel数据 -->
        <el-table v-loading="exloading" :data="tExFishInfo">
          <el-table-column label="行数" align="center" prop="rowIndex" :show-overflow-tooltip="true" />
          <el-table-column label="中文名称" align="center" prop="chName" :show-overflow-tooltip="true" />
          <el-table-column label="英文名称" align="center" prop="enName" :show-overflow-tooltip="true" />
          <el-table-column label="规则" align="center" prop="rule" :show-overflow-tooltip="true" />
          <el-table-column label="最大KG(LB)" align="center" prop="weight" :show-overflow-tooltip="true" />
          <el-table-column label="长度(CM)" align="center" prop="length" :show-overflow-tooltip="true" />
          <el-table-column label="价格/KG" align="center" prop="price" :show-overflow-tooltip="true" />
          <el-table-column label="鱼饵类型" align="center" prop="baitType" :show-overflow-tooltip="true" />
          <el-table-column label="假饵类型" align="center" prop="lureType" :show-overflow-tooltip="true" />
          <el-table-column label="生活区域" align="center" prop="liveArea" :show-overflow-tooltip="true" />
          <el-table-column label="食物" align="center" prop="food" :show-overflow-tooltip="true" />
          <el-table-column label="其他特性" align="center" prop="feature" :show-overflow-tooltip="true" />
          <el-table-column label="出没场景" align="center" prop="scene" :show-overflow-tooltip="true" />
          <el-table-column label="图片地址" align="center" prop="imageUrl" :show-overflow-tooltip="true" />
          <el-table-column label="创建者名称" align="center" prop="createName" :show-overflow-tooltip="true" />
        </el-table>
        <pagination
          v-show="exTotal>0"
          :total="exTotal"
          :page.sync="exPageIndex"
          :limit.sync="exPageSize"
          @pagination="getExList"
        />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px">
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">

            <el-form-item label="中文名称" prop="chName">
              <el-input v-model="form.chName" placeholder="中文名称" />
            </el-form-item>
            <el-form-item label="英文名称" prop="enName">
              <el-input v-model="form.enName" placeholder="英文名称" />
            </el-form-item>
            <el-form-item label="规则" prop="rule">
              <el-input v-model="form.rule" placeholder="规则" />
            </el-form-item>
            <el-form-item label="最大KG(LB)" prop="weight">
              <el-input v-model="form.weight" placeholder="最大KG(LB)" />
            </el-form-item>
            <el-form-item label="长度(CM)" prop="length">
              <el-input v-model="form.length" placeholder="长度(CM)" />
            </el-form-item>
            <el-form-item label="价格/KG" prop="price">
              <el-input v-model="form.price" placeholder="价格/KG" />
            </el-form-item>
            <el-form-item label="鱼饵类型" prop="baitType">
              <el-input v-model="form.baitType" placeholder="鱼饵类型" />
            </el-form-item>
            <el-form-item label="假饵类型" prop="lureType">
              <el-input v-model="form.lureType" placeholder="假饵类型" />
            </el-form-item>
            <el-form-item label="生活区域" prop="liveArea">
              <el-input v-model="form.liveArea" placeholder="生活区域" />
            </el-form-item>
            <el-form-item label="食物" prop="food">
              <el-input v-model="form.food" placeholder="食物" />
            </el-form-item>
            <el-form-item label="其他特性" prop="feature">
              <el-input v-model="form.feature" placeholder="其他特性" />
            </el-form-item>
            <el-form-item label="出没场景" prop="scene">
              <el-input v-model="form.scene" placeholder="出没场景" />
            </el-form-item>
            <el-form-item label="图片地址" prop="imageUrl">
              <el-input v-model="form.imageUrl" placeholder="图片地址" />
            </el-form-item>
            <el-form-item label="创建者名称" prop="createName">
              <el-input v-model="form.createName" placeholder="创建者名称" />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { addTFishInfo, delTFishInfo, getTFishInfo, listTFishInfo, updateTFishInfo, excelLoadToDb } from '@/api/plant/fishinfo'

import XLSX from 'xlsx'

export default {
  name: 'TFishInfo',
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      tFishInfoList: [],

      exloading: false,

      // excel展示数据
      tExFishInfo: [],

      // 写入excel数据
      tDBFishInfo: [],

      // excel里面的数据 默认第一个sheet表
      exFileData: [],

      // excel文件
      exFileName: '',

      // excel翻页
      exTotal: 0,
      exPageIndex: 1,
      exPageSize: 10,

      // 关系表类型

      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        chName: undefined,
        enName: undefined

      },
      // 表单参数
      form: {
      },
      // 表单校验
      rules: { chName: [{ required: true, message: '中文名称不能为空', trigger: 'blur' }],
        enName: [{ required: true, message: '英文名称不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {

    /** 查询excel数据列表 */
    getExList() {
      this.exloading = true
      this.tExFishInfo = this.compExcelToFishInfo(this.exFileData)
      this.exTotal = this.tExFishInfo.length
      this.exloading = false
      console.log('getList:', this.tExFishInfo)
    },
    /** 选取excel表格 */
    exFileChange(file) {
      this.exFileName = file.raw
      console.log('exFileChange:', this.exFileName)
      this.loadExcelFile(this.exFileName)
    },

    /** 导出excel数据 */
    submitForm2: function() {
      console.log('submitForm2:', this.exFileData)
      this.getExList()
    },

    /** 写入数据库 */
    submitForm3: function() {
      console.log('submitForm3:', this.tDBFishInfo)
      excelLoadToDb(this.tDBFishInfo).then(response => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          // 重新刷新数据库数据
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      })
    },

    /** 组合excel数据到 tExFishInfo */
    compExcelToFishInfo(exFileData) {
      console.log('compExcelToFishInfo1:', exFileData, '==', exFileData.length)

      this.tDBFishInfo = []
      var tempExFishInfo = []
      var header = ['chName', 'enName', 'rule', 'weight', 'length', 'price', 'baitType', 'lureType', 'liveArea', 'food', 'feature', 'scene', 'imageUrl', 'createName']
      console.log('compExcelToFishInfo6:', header.length)
      for (let i = 1; i < exFileData.length; i++) {
        var tempValue = exFileData[i]
        var tempMap = {}
        var tempDbMap = {}
        for (let j = 0; j < header.length && j < tempValue.length; j++) {
          tempMap['rowIndex'] = i
          tempMap[header[j]] = tempValue[j]
        }

        // 构建db数据[{string:string}]
        for (let k = 0; k < header.length; k++) {
          // 如果是int类型转换为string
          if (parseInt(tempValue[k]) === tempValue[k]) {
            tempValue[k] = tempValue[k].toString()
          }
          if (k < tempValue.length) {
            tempDbMap[header[k]] = tempValue[k]
          } else {
            tempDbMap[header[k]] = null
          }
        }
        tempDbMap['createName'] = 'fromExcel'
        tempExFishInfo.push(tempMap)
        this.tDBFishInfo.push(tempDbMap)
      }
      console.log('compExcelToFishInfo3', tempExFishInfo, this.tempDbMap)
      return tempExFishInfo
    },

    /** 解析excel数据 */
    loadExcelFile(exFile) {
      console.log('loadExcelFile:', exFile)

      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = e => {
          const data = e.target.result
          const workbook = XLSX.read(data, { type: 'array' })
          const configSheetNames = workbook.SheetNames
          console.log('loadExcelFile1:', configSheetNames.length)

          // 只取第一个sheet表
          if (configSheetNames.length <= 0) {
            return
          }

          const sheetName = workbook.SheetNames[0]
          const aHeadCenum = sheetName.startsWith('c.')

          const worksheet = workbook.Sheets[sheetName]
          const result = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

          if (aHeadCenum) {
            // 转换Const为列
            var newHeader = []
            var newRow = []
            for (let index = 1; index < result.length; index++) { // 过滤const的标题
              const element = result[index]
              newRow[index - 1] = element[0]
              newHeader[index - 1] = element[2]
            }
            var newResult = []
            newResult.push(newHeader)
            newResult.push(newRow)

            this.exFileData = newResult
            console.log('loadExcelFile1:', this.exFileData)
          } else {
            this.exFileData = result
            console.log('loadExcelFile2:', sheetName, this.exFileData)
          }
          resolve()
        }
        reader.readAsArrayBuffer(exFile)
      })
    },

    /** 查询参数列表 */
    getList() {
      this.loading = true
      listTFishInfo(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.tFishInfoList = response.data.list
        this.total = response.data.count
        this.loading = false
        console.log('getList:', this.tFishInfoList)
      }
      )
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {

        id: undefined,
        chName: undefined,
        enName: undefined,
        rule: undefined,
        weight: undefined,
        length: undefined,
        price: undefined,
        baitType: undefined,
        lureType: undefined,
        liveArea: undefined,
        food: undefined,
        feature: undefined,
        scene: undefined,
        imageUrl: undefined,
        createName: undefined
      }
      this.resetForm('form')
    },
    getImgList: function() {
      this.form[this.fileIndex] = this.$refs['fileChoose'].resultList[0].fullUrl
    },
    fileClose: function() {
      this.fileOpen = false
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加鱼种信息'
      this.isEdit = false
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id =
        row.id || this.ids
      getTFishInfo(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改鱼种信息'
        this.isEdit = true
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id !== undefined) {
            updateTFishInfo(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          } else {
            console.log('this->form', this.form)
            addTFishInfo(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var Ids = (row.id && [row.id]) || this.ids

      this.$confirm('是否确认删除编号为"' + Ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delTFishInfo({ 'ids': Ids })
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function() {
      })
    }
  }
}

</script>
@/api/plant/fishinfo
