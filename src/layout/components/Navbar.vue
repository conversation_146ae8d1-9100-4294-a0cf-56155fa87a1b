<template>
  <div class="navbar">

    <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />
    <top-nav v-if="topNav" id="topmenu-container" class="breadcrumb-container" />
    <div class="platform-pop">
      <div class="topLeft">
        <el-cascader
          v-model="selectProject"
          style="width: 270px;"
          placeholder="请选择产品渠道环境"
          :options="envAndPlatform"
          :props="{ expandTrigger: 'hover' }"
          @change="handleSelectChange"
        />
      </div>
      <div class="topRight">
        <breadcrumb v-if="!topNav" id="breadcrumb-container" class="breadcrumb-container" />
      </div>

    </div>
    <div class="right-menu">
      <template v-if="device!=='mobile'">
        <search id="header-search" class="right-menu-item" />

        <screenfull id="screenfull" class="right-menu-item hover-effect" />

      </template>

      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="hover">
        <div class="avatar-wrapper">
          <img :src="avatar+'?imageView2/1/w/80/h/80'" class="user-avatar">
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown">
          <router-link to="/profile/index">
            <el-dropdown-item>个人中心</el-dropdown-item>
          </router-link>
          <el-dropdown-item divided>
            <span style="display:block;" @click="logout">退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import TopNav from '@/components/TopNav'
import Hamburger from '@/components/Hamburger'
import Screenfull from '@/components/Screenfull'
import Search from '@/components/HeaderSearch'
import { myProjectEnvPlanet } from '@/api/admin/project'

export default {
  components: {
    Breadcrumb,
    TopNav,
    Hamburger,
    Screenfull,
    Search
  },
  data() {
    return {
      visible: false,
      envAndPlatform: [],
      selectProject: [],
      // 环境字典列表
      envWordList: []
    }
  },
  created() {
    // const loading = this.$loading({
    //       lock: true,
    //       text: '加载中，请稍候......',
    //       spinner: 'el-icon-loading',
    //       background: 'rgba(0, 0, 0, 0.7)'
    //     });
    // setTimeout(() => {
    //     loading.close();
    //   }, 1000);

    this.getDicts('env_type').then(response => {
      this.envWordList = response.data

      this.getMyProjectEnvPlanet()
      const pList = this.getProjectFromLocal()
      this.selectProject = this.projectBackToValue(pList)
      this.$store.dispatch('user/setProject', pList)
      console.log('this.selectProject:', this.selectProject, ' userId:', this.userId)
    }).catch(error => {
      console.log(error)
      this.$message({
        showClose: true,
        duration: 0,
        message: '🚨项目及环境数据加载失败，刷新重试！',
        type: 'error'
      })
    })
  },
  // eslint-disable-next-line vue/order-in-components
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar',
      'device',
      'project',
      'userId'
    ]),
    setting: {
      get() {
        return this.$store.state.settings.showSettings
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'showSettings',
          value: val
        })
      }
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav
      }
    }
  },
  watch: {
    // 监视变化
    selectProject(newVal) {
      console.log('watch : ', newVal)
      if (newVal.length === 0) {
        this.$message({
          showClose: true,
          duration: 0,
          message: '🚨没有选择产品环境渠道，请选择！',
          type: 'warning'
        })
      }
    }
  },
  methods: {
    getUserKey() {
      return this.userId + '_project'
    },
    envWordShow(env) {
      let envName = ''
      for (let index = 0; index < this.envWordList.length; index++) {
        const keyRow = this.envWordList[index]
        var envId = parseInt(keyRow.value)
        if (envId === env) {
          envName = keyRow.label
          break
        }
      }
      return envName
    },
    saveProjectToLocal(project) {
      localStorage.setItem(this.getUserKey(), JSON.stringify(project))
    },
    projectBackToValue(project) {
      if (project === undefined || project === null) {
        return []
      }
      const list = []
      for (let i = 0; i < project.length; i++) {
        let item = project[i]
        if (i === 0) {
          item = 'pro_' + item
        } else if (i === 1) {
          item = 'env_' + item
        } else {
          item = 'planet_' + item
        }
        list.push(item)
      }
      // console.log('projectBackToValue', list)
      return list
    },
    getProjectFromLocal() {
      const str = localStorage.getItem(this.getUserKey())
      if (str === undefined || str === '') {
        return []
      }
      try {
        return JSON.parse(str)
      } catch (e) {
        return []
      }
    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    handleSelectChange(value) {
      console.log('selectProj', this.selectProject)
      const list = []
      for (const item of value) {
        const tmp = item.split('_')
        list.push(parseInt(tmp[1]))
      }

      // console.log(list)
      this.saveProjectToLocal(list)
      this.$store.dispatch('user/setProject', list)
      // 提交
    },
    openMask() {
      console.log('open mask')
      const mask = document.createElement('div')
      mask.className = 'c-mask'
      mask.id = 'c-mask'
      document.body.appendChild(mask)
    },
    closeMask() {
      this.visible = false
      const mask = document.getElementById('c-mask')
      mask.parentNode.removeChild(mask)
    },
    getMyProjectEnvPlanet() {
      // this.envAndPlatform = []
      myProjectEnvPlanet().then((response) => {
        // console.log(response)
        if (!response || !response.data) {
          this.$message({
            message: '项目已经环境信息加载失败',
            type: 0
          })
          return
        }

        console.log('getMyProjectEnvPlanet rsp:', response.data)

        const pepList = []
        for (const i in response.data) {
          const project = response.data[i]
          const pNode = {
            id: 'pro_' + project.id,
            value: 'pro_' + project.id,
            nodeID: project.id,
            label: project.name,
            isPlanet: false,
            children: []
          }
          for (const ie in project.envList) {
            const env = project.envList[ie]
            const eNode = {
              id: 'env_' + env.env_type,
              value: 'env_' + env.env_type,
              nodeID: env.env_type,
              // label: env.envZhName + '(' + env.envName + ')',
              label: this.envWordShow(env.env_type),
              isPlanet: false,
              children: []
            }
            for (const ip in env.planetList) {
              const planet = env.planetList[ip]
              const planetNode = {
                id: 'planet_' + planet.ID,
                value: 'planet_' + planet.ID,
                nodeID: planet.ID,
                isPlanet: true,
                label: planet.planetName + '(' + planet.ID + ')'
              }
              eNode.children.push(planetNode)
            }
            pNode.children.push(eNode)
          }

          pepList.push(pNode)
        }

        this.envAndPlatform = pepList
        console.log('this.envAndPlatform', this.envAndPlatform)
      })
    },

    async logout() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('user/LogOut').then(() => {
          location.reload()
        })
      })
    }
  }
}
</script>

<style lang="scss" >
.platform-pop {
  display: inline-flex;
}
.topRight {
  flex: 1;
  text-align: right;
}

.topLeft {
  flex: 1;
  text-align: left;
  margin-top: 8px;
}

.c-mask {
  z-index: 1001;
  position: fixed;
  top: 0;
  left: 0;
  background-color: rgba(18,18,18,.65);
  width: 100vw;
  height: 100vh;
}
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color:transparent;

    &:hover {
      background: rgba(0, 0, 0, .025)
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 50%;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 15px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>

