import request from '@/utils/request'

// 查询TBasePropOds列表
export function listTBasePropOds(query) {
  return request({
    url: '/api/v1/t-base-prop-ods',
    method: 'get',
    params: query
  })
}

// 查询TBasePropOds详细
export function getTBasePropOds(uid) {
  return request({
    url: '/api/v1/t-base-prop-ods/' + uid,
    method: 'get'
  })
}

// 新增TBasePropOds
export function addTBasePropOds(data) {
  return request({
    url: '/api/v1/t-base-prop-ods',
    method: 'post',
    data: data
  })
}

// 修改TBasePropOds
export function updateTBasePropOds(data) {
  return request({
    url: '/api/v1/t-base-prop-ods/' + data.uid,
    method: 'put',
    data: data
  })
}

// 删除TBasePropOds
export function delTBasePropOds(data) {
  return request({
    url: '/api/v1/t-base-prop-ods',
    method: 'delete',
    data: data
  })
}

