import request from '@/utils/request'

// 查询TPlayer列表
export function listTPlayer(query) {
  return request({
    url: '/api/v1/player',
    method: 'get',
    params: query
  })
}

// 查询TPlayer详细
export function getTPlayer(uid) {
  return request({
    url: '/api/v1/player/' + uid,
    method: 'get'
  })
}

// 新增TPlayer
export function addTPlayer(data) {
  return request({
    url: '/api/v1/player',
    method: 'post',
    data: data
  })
}

// 修改TPlayer
export function updateTPlayer(data) {
  return request({
    url: '/api/v1/player/' + data.uid,
    method: 'put',
    data: data
  })
}

// 删除TPlayer
export function delTPlayer(data) {
  return request({
    url: '/api/v1/player',
    method: 'delete',
    data: data
  })
}

