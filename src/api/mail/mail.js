import request from '@/utils/request'

// 查询TMailMaster列表
export function listTMailMaster(query) {
  return request({
    url: '/api/v1/t-mail-master',
    method: 'get',
    params: query
  })
}

// 查询TMailMaster详细
export function getTMailMaster(id) {
  return request({
    url: '/api/v1/t-mail-master/' + id,
    method: 'get'
  })
}

// 新增TMailMaster
export function addTMailMaster(data) {
  return request({
    url: '/api/v1/t-mail-master',
    method: 'post',
    data: data
  })
}

// 修改TMailMaster
export function updateTMailMaster(data) {
  return request({
    url: '/api/v1/t-mail-master/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TMailMaster
export function delTMailMaster(data) {
  return request({
    url: '/api/v1/t-mail-master',
    method: 'delete',
    data: data
  })
}

