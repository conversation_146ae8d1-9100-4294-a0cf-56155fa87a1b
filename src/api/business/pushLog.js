import request from '@/utils/request'

// 查询TPushLog列表
export function listTPushLog(query) {
  return request({
    url: '/api/v1/t-push-log',
    method: 'get',
    params: query,
    timeout: 30000
  })
}

// 查询TPushLog详细
export function getTPushLog(id) {
  return request({
    url: '/api/v1/t-push-log/' + id,
    method: 'get'
  })
}

// 新增TPushLog
export function addTPushLog(data) {
  return request({
    url: '/api/v1/t-push-log',
    method: 'post',
    data: data
  })
}

// 修改TPushLog
export function updateTPushLog(data) {
  return request({
    url: '/api/v1/t-push-log/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TPushLog
export function delTPushLog(data) {
  return request({
    url: '/api/v1/t-push-log',
    method: 'delete',
    data: data
  })
}

