import request from '@/utils/request'

// 查询TMockLogin列表
export function listTMockLogin(query) {
  return request({
    url: '/api/v1/t-mock-login',
    method: 'get',
    params: query
  })
}

// 查询TMockLogin详细
export function getTMockLogin(id) {
  return request({
    url: '/api/v1/t-mock-login/' + id,
    method: 'get'
  })
}

// 新增TMockLogin
export function addTMockLogin(data) {
  return request({
    url: '/api/v1/t-mock-login',
    method: 'post',
    data: data
  })
}

// 修改TMockLogin
export function updateTMockLogin(data) {
  return request({
    url: '/api/v1/t-mock-login/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TMockLogin
export function delTMockLogin(data) {
  return request({
    url: '/api/v1/t-mock-login',
    method: 'delete',
    data: data
  })
}

