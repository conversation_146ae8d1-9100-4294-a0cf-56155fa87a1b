import request from '@/utils/request'

// 查询TFansPage列表
export function listTFansPage(query) {
  return request({
    url: '/api/v1/t-fans-page',
    method: 'get',
    params: query
  })
}

// 查询TFansPage详细
export function getTFansPage(id) {
  return request({
    url: '/api/v1/t-fans-page/' + id,
    method: 'get'
  })
}

// 新增TFansPage
export function addTFansPage(data) {
  return request({
    url: '/api/v1/t-fans-page',
    method: 'post',
    data: data
  })
}

// 修改TFansPage
export function updateTFansPage(data) {
  return request({
    url: '/api/v1/t-fans-page/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TFansPage
export function delTFansPage(data) {
  return request({
    url: '/api/v1/t-fans-page',
    method: 'delete',
    data: data
  })
}

