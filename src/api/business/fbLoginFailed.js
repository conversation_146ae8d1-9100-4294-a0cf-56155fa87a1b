import request from '@/utils/request'

// 查询TFbLoginFailed列表
export function listTFbLoginFailed(query) {
  return request({
    url: '/api/v1/t-fb-login-failed',
    method: 'get',
    params: query
  })
}

// 查询根据Error汇总列表
export function listGroupTFbLoginFailed(query) {
  return request({
    url: '/api/v1/t-fb-login-failed-Group',
    method: 'get',
    params: query
  })
}

// 查询TFbLoginFailed详细
export function getTFbLoginFailed(id) {
  return request({
    url: '/api/v1/t-fb-login-failed/' + id,
    method: 'get'
  })
}

// 新增TFbLoginFailed
export function addTFbLoginFailed(data) {
  return request({
    url: '/api/v1/t-fb-login-failed',
    method: 'post',
    data: data
  })
}

// 修改TFbLoginFailed
export function updateTFbLoginFailed(data) {
  return request({
    url: '/api/v1/t-fb-login-failed/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TFbLoginFailed
export function delTFbLoginFailed(data) {
  return request({
    url: '/api/v1/t-fb-login-failed',
    method: 'delete',
    data: data
  })
}

