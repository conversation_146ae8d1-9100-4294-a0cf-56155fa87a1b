import request from '@/utils/request'

// 查询拍脸图配置列表
export function addOperatePopup(data) {
  return request({
    url: '/api/v1/msg_ann_popup',
    method: 'post',
    data: data
  })
}
export function deleteOperatePopup(query) {
  return request({
    url: '/api/v1/msg_ann_popup',
    method: 'delete',
    params: query
  })
}
export function listOperatePopup(query) {
  return request({
    url: '/api/v1/msg_ann_popup',
    method: 'get',
    params: query
  })
}
export function updateOperatePopup(data) {
  return request({
    url: '/api/v1/msg_ann_popup',
    method: 'post',
    data: data
  })
}

// 查询TMailMaster详细
export function getTMailMaster(id) {
  return request({
    url: '/api/v1/t-mail-master/' + id,
    method: 'get'
  })
}

// 新增TMailMaster
export function addTMailMaster(data) {
  return request({
    url: '/api/v1/t-mail-master',
    method: 'post',
    data: data
  })
}

// 删除TMailMaster
export function delTMailMaster(data) {
  return request({
    url: '/api/v1/t-mail-master',
    method: 'delete',
    data: data
  })
}

