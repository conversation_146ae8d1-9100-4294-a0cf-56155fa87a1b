import request from '@/utils/request'

// 查询CDK批次列表
export function listCdkBatches(query) {
  return request({
    url: '/api/v1/cdk/batches',
    method: 'get',
    params: query
  })
}

// 查询CDK记录列表
export function listCdkRecords(query) {
  return request({
    url: '/api/v1/cdk/records',
    method: 'get',
    params: query
  })
}

// 创建CDK批次
export function createCdkBatch(data) {
  return request({
    url: '/api/v1/cdk/batch',
    method: 'post',
    data: data
  })
}

// 作废CDK批次
export function disableCdkBatch(query) {
  return request({
    url: '/api/v1/cdk/batch/disable',
    method: 'put',
    params: query
  })
}
