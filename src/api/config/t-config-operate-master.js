import request from '@/utils/request'

// 查询TConfigOperateMaster列表
export function listTConfigOperateMaster(query) {
  return request({
    url: '/api/v1/t-config-operate-master',
    method: 'get',
    params: query
  })
}

// 查询TConfigOperateMaster详细
export function getTConfigOperateMaster(id) {
  return request({
    url: '/api/v1/t-config-operate-master/' + id,
    method: 'get'
  })
}

// 新增TConfigOperateMaster
export function addTConfigOperateMaster(data) {
  return request({
    url: '/api/v1/t-config-operate-master',
    method: 'post',
    data: data
  })
}

// 修改TConfigOperateMaster
export function updateTConfigOperateMaster(data) {
  return request({
    url: '/api/v1/t-config-operate-master/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TConfigOperateMaster
export function delTConfigOperateMaster(data) {
  return request({
    url: '/api/v1/t-config-operate-master',
    method: 'delete',
    data: data
  })
}

// Commit TConfigOperateMaster列表
export function commitConfigOperateMaster(query) {
  return request({
    url: '/api/v1/config-handler',
    method: 'get',
    params: query
  })
}

