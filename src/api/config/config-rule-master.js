import request from '@/utils/request'

// 查询ConfigRuleMaster列表
export function listConfigRuleMaster(query) {
  return request({
    url: '/api/v1/config-rule-master',
    method: 'get',
    params: query
  })
}

// 查询ConfigRuleMaster详细
export function getConfigRuleMaster(id) {
  return request({
    url: '/api/v1/config-rule-master/' + id,
    method: 'get'
  })
}

// 新增ConfigRuleMaster
export function addConfigRuleMaster(data) {
  return request({
    url: '/api/v1/config-rule-master',
    method: 'post',
    data: data
  })
}

// 修改ConfigRuleMaster
export function updateConfigRuleMaster(data) {
  return request({
    url: '/api/v1/config-rule-master/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除ConfigRuleMaster
export function delConfigRuleMaster(data) {
  return request({
    url: '/api/v1/config-rule-master',
    method: 'delete',
    data: data
  })
}

