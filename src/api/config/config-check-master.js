import request from '@/utils/request'

// 查询ConfigCheckMaster列表
export function listConfigCheckMaster(query) {
  return request({
    url: '/api/v1/config-check-master',
    method: 'get',
    params: query
  })
}

// 查询ConfigCheckMaster详细
export function getConfigCheckMaster(id) {
  return request({
    url: '/api/v1/config-check-master/' + id,
    method: 'get'
  })
}

// 新增ConfigCheckMaster
export function addConfigCheckMaster(data) {
  return request({
    url: '/api/v1/config-check-master',
    method: 'post',
    data: data
  })
}

// 修改ConfigCheckMaster
export function updateConfigCheckMaster(data) {
  return request({
    url: '/api/v1/config-check-master/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除ConfigCheckMaster
export function delConfigCheckMaster(data) {
  return request({
    url: '/api/v1/config-check-master',
    method: 'delete',
    data: data
  })
}

