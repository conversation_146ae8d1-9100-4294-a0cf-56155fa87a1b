import request from '@/utils/request'

// 查询TConfigAuditMaster列表
export function listTConfigAuditMaster(query) {
  return request({
    url: '/api/v1/t-config-audit-master',
    method: 'get',
    params: query
  })
}

// 查询TConfigAuditMaster详细
export function getTConfigAuditMaster(id) {
  return request({
    url: '/api/v1/t-config-audit-master/' + id,
    method: 'get'
  })
}

// 新增TConfigAuditMaster
export function addTConfigAuditMaster(data) {
  return request({
    url: '/api/v1/t-config-audit-master',
    method: 'post',
    data: data
  })
}

// 修改TConfigAuditMaster
export function updateTConfigAuditMaster(data) {
  return request({
    url: '/api/v1/t-config-audit-master/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TConfigAuditMaster
export function delTConfigAuditMaster(data) {
  return request({
    url: '/api/v1/t-config-audit-master',
    method: 'delete',
    data: data
  })
}

