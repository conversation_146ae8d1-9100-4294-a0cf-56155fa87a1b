import request from '@/utils/request'

// 查询ConfigFieldMaster列表
export function listConfigFieldMaster(query) {
  return request({
    url: '/api/v1/config-field-master',
    method: 'get',
    params: query
  })
}

// 查询ConfigFieldMaster详细
export function getConfigFieldMaster(id) {
  return request({
    url: '/api/v1/config-field-master/' + id,
    method: 'get'
  })
}

// 新增ConfigFieldMaster
export function addConfigFieldMaster(data) {
  return request({
    url: '/api/v1/config-field-master',
    method: 'post',
    data: data
  })
}

// Excel 导入 ConfigFieldMaster
export function addConfigFieldByExcel(data) {
  return request({
    url: '/api/v1/config-field-master-excel',
    method: 'post',
    data: data
  })
}

// 修改ConfigFieldMaster
export function updateConfigFieldMaster(data) {
  return request({
    url: '/api/v1/config-field-master/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除ConfigFieldMaster
export function delConfigFieldMaster(data) {
  return request({
    url: '/api/v1/config-field-master',
    method: 'delete',
    data: data
  })
}

