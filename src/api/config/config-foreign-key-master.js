import request from '@/utils/request'

// 查询ConfigForeignKeyMaster列表
export function listConfigForeignKeyMaster(query) {
  return request({
    url: '/api/v1/config-foreign-key-master',
    method: 'get',
    params: query
  })
}

// 查询ConfigForeignKeyMaster详细
export function getConfigForeignKeyMaster(id) {
  return request({
    url: '/api/v1/config-foreign-key-master/' + id,
    method: 'get'
  })
}

// 新增ConfigForeignKeyMaster
export function addConfigForeignKeyMaster(data) {
  return request({
    url: '/api/v1/config-foreign-key-master',
    method: 'post',
    data: data
  })
}

// 修改ConfigForeignKeyMaster
export function updateConfigForeignKeyMaster(data) {
  return request({
    url: '/api/v1/config-foreign-key-master/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除ConfigForeignKeyMaster
export function delConfigForeignKeyMaster(data) {
  return request({
    url: '/api/v1/config-foreign-key-master',
    method: 'delete',
    data: data
  })
}

// Excel 导入 ConfigForeignKey
export function addConfigForeignKeyByExcel(data) {
  return request({
    url: '/api/v1/config-foreign-key-master-excel',
    method: 'post',
    data: data
  })
}

