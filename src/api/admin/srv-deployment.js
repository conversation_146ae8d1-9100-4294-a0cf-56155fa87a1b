import request from '@/utils/request'

export function nodeList() {
  return request({
    url: '/api/v1/srv-deployment/nodeList',
    method: 'get'
  })
}

export function nodeSetColor(data) {
  return request({
    url: '/api/v1/srv-deployment/nodeSetColor',
    method: 'put',
    data: data
  })
}

export function nodeStop(data) {
  return request({
    url: '/api/v1/srv-deployment/nodeStop',
    method: 'put',
    data: data
  })
}
