import request from '@/utils/request'

// 查询TEnv列表
export function listTEnv(query) {
  return request({
    url: '/api/v1/env',
    method: 'get',
    params: query
  })
}

// 查询TEnv详细
export function getTEnv(id) {
  return request({
    url: '/api/v1/env/' + id,
    method: 'get'
  })
}

// 新增TEnv
export function addTEnv(data) {
  return request({
    url: '/api/v1/env',
    method: 'post',
    data: data
  })
}

// 修改TEnv
export function updateTEnv(data) {
  return request({
    url: '/api/v1/env/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TEnv
export function delTEnv(data) {
  return request({
    url: '/api/v1/env',
    method: 'delete',
    data: data
  })
}

