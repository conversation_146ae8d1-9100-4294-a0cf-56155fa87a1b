import request from '@/utils/request'

// 查询Planet列表
export function listPlanet(query) {
  return request({
    url: '/api/v1/planet',
    method: 'get',
    params: query
  })
}

// 查询Planet详细
export function getPlanet(id) {
  return request({
    url: '/api/v1/planet/' + id,
    method: 'get'
  })
}

// 新增Planet
export function addPlanet(data) {
  return request({
    url: '/api/v1/planet',
    method: 'post',
    data: data
  })
}

// 修改Planet
export function updatePlanet(data) {
  return request({
    url: '/api/v1/planet/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除Planet
export function delPlanet(data) {
  return request({
    url: '/api/v1/planet',
    method: 'delete',
    data: data
  })
}

