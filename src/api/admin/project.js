import request from '@/utils/request'

// 查询TProject列表
export function listTProject(query) {
  return request({
    url: '/api/v1/project',
    method: 'get',
    params: query
  })
}

// 查询TProject详细
export function getTProject(id) {
  return request({
    url: '/api/v1/project/' + id,
    method: 'get'
  })
}

// 查询TProject详细
export function getAllCompleteInfo() {
  return request({
    url: '/api/v1/project/getAllCompleteInfo',
    method: 'get'
  })
}

// 查询TProject详细
export function myProjectEnvPlanet() {
  return request({
    url: '/api/v1/project/myProjectEnvPlanet',
    method: 'get'
  })
}

// 新增TProject
export function addTProject(data) {
  return request({
    url: '/api/v1/project',
    method: 'post',
    data: data
  })
}

// 修改TProject
export function updateTProject(data) {
  return request({
    url: '/api/v1/project/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TProject
export function delTProject(data) {
  return request({
    url: '/api/v1/project',
    method: 'delete',
    data: data
  })
}

