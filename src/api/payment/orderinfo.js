import request from '@/utils/request'

// TODO 清空操作日志
export function cleanOperlog() {
  return request({
    url: '/api/v1/operlog/clean',
    method: 'delete'
  })
}

// 查询Order列表
export function listOrder(query) {
  return request({
    url: '/api/v1/list',
    method: 'get',
    params: query
  })
}

// // 删除SysOperlog
// export function delSysOperlog(data) {
//   return request({
//     url: '/api/v1/payment',
//     method: 'delete',
//     data: data
//   })
// }

