import request from '@/utils/request'

// 查询TFishInfo列表
export function listTFishInfo(query) {
  return request({
    url: '/api/v1/fishinfo',
    method: 'get',
    params: query
  })
}

// 查询TFishInfo详细
export function getTFishInfo(id) {
  return request({
    url: '/api/v1/fishinfo/' + id,
    method: 'get'
  })
}

// 新增TFishInfo
export function addTFishInfo(data) {
  return request({
    url: '/api/v1/fishinfo',
    method: 'post',
    data: data
  })
}

// 修改TFishInfo
export function updateTFishInfo(data) {
  return request({
    url: '/api/v1/fishinfo/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TFishInfo
export function delTFishInfo(data) {
  return request({
    url: '/api/v1/fishinfo',
    method: 'delete',
    data: data
  })
}

// 导入到db
export function excelLoadToDb(data) {
  return request({
    url: '/api/v1/fishinfo2Db',
    method: 'post',
    data: data
  })
}
