import request from '@/utils/request'

// 查询TWhiteList列表
export function listTWhiteList(query) {
  return request({
    url: '/api/v1/t-white-list',
    method: 'get',
    params: query
  })
}

// 查询TWhiteList详细
export function getTWhiteList(id) {
  return request({
    url: '/api/v1/t-white-list/' + id,
    method: 'get'
  })
}

// 新增TWhiteList
export function addTWhiteList(data) {
  return request({
    url: '/api/v1/t-white-list',
    method: 'post',
    data: data
  })
}

// 修改TWhiteList
export function updateTWhiteList(data) {
  return request({
    url: '/api/v1/t-white-list/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TWhiteList
export function delTWhiteList(data) {
  return request({
    url: '/api/v1/t-white-list',
    method: 'delete',
    data: data
  })
}

