import request from '@/utils/request'

// 查询TPlantParams列表
export function listTPlantParams(query) {
  return request({
    url: '/api/v1/t-plant-params',
    method: 'get',
    params: query
  })
}

// 查询TPlantParams详细
export function getTPlantParams(id) {
  return request({
    url: '/api/v1/t-plant-params/' + id,
    method: 'get'
  })
}

// 新增TPlantParams
export function addTPlantParams(data) {
  return request({
    url: '/api/v1/t-plant-params',
    method: 'post',
    data: data
  })
}

// 修改TPlantParams
export function updateTPlantParams(data) {
  return request({
    url: '/api/v1/t-plant-params/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TPlantParams
export function delTPlantParams(data) {
  return request({
    url: '/api/v1/t-plant-params',
    method: 'delete',
    data: data
  })
}

