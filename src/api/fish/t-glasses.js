import request from '@/utils/request'

// 查询TGlasses列表
export function listTGlasses(query) {
  return request({
    url: '/api/v1/t-glasses',
    method: 'get',
    params: query
  })
}

// 查询TGlasses详细
export function getTGlasses(id) {
  return request({
    url: '/api/v1/t-glasses/' + id,
    method: 'get'
  })
}

// 新增TGlasses
export function addTGlasses(data) {
  return request({
    url: '/api/v1/t-glasses',
    method: 'post',
    data: data
  })
}

// 修改TGlasses
export function updateTGlasses(data) {
  return request({
    url: '/api/v1/t-glasses/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TGlasses
export function delTGlasses(data) {
  return request({
    url: '/api/v1/t-glasses',
    method: 'delete',
    data: data
  })
}

