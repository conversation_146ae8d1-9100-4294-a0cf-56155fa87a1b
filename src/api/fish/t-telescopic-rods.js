import request from '@/utils/request'

// 查询TTelescopicRods列表
export function listTTelescopicRods(query) {
  return request({
    url: '/api/v1/t-telescopic-rods',
    method: 'get',
    params: query
  })
}

// 查询TTelescopicRods详细
export function getTTelescopicRods(id) {
  return request({
    url: '/api/v1/t-telescopic-rods/' + id,
    method: 'get'
  })
}

// 新增TTelescopicRods
export function addTTelescopicRods(data) {
  return request({
    url: '/api/v1/t-telescopic-rods',
    method: 'post',
    data: data
  })
}

// 修改TTelescopicRods
export function updateTTelescopicRods(data) {
  return request({
    url: '/api/v1/t-telescopic-rods/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TTelescopicRods
export function delTTelescopicRods(data) {
  return request({
    url: '/api/v1/t-telescopic-rods',
    method: 'delete',
    data: data
  })
}

