import request from '@/utils/request'

// 查询TMonoLeaders列表
export function listTMonoLeaders(query) {
  return request({
    url: '/api/v1/t-mono-leaders',
    method: 'get',
    params: query
  })
}

// 查询TMonoLeaders详细
export function getTMonoLeaders(id) {
  return request({
    url: '/api/v1/t-mono-leaders/' + id,
    method: 'get'
  })
}

// 新增TMonoLeaders
export function addTMonoLeaders(data) {
  return request({
    url: '/api/v1/t-mono-leaders',
    method: 'post',
    data: data
  })
}

// 修改TMonoLeaders
export function updateTMonoLeaders(data) {
  return request({
    url: '/api/v1/t-mono-leaders/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TMonoLeaders
export function delTMonoLeaders(data) {
  return request({
    url: '/api/v1/t-mono-leaders',
    method: 'delete',
    data: data
  })
}

