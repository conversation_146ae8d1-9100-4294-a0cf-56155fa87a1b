import request from '@/utils/request'

// 查询TCarpRods列表
export function listTCarpRods(query) {
  return request({
    url: '/api/v1/t-carp-rods',
    method: 'get',
    params: query
  })
}

// 查询TCarpRods详细
export function getTCarpRods(id) {
  return request({
    url: '/api/v1/t-carp-rods/' + id,
    method: 'get'
  })
}

// 新增TCarpRods
export function addTCarpRods(data) {
  return request({
    url: '/api/v1/t-carp-rods',
    method: 'post',
    data: data
  })
}

// 修改TCarpRods
export function updateTCarpRods(data) {
  return request({
    url: '/api/v1/t-carp-rods/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TCarpRods
export function delTCarpRods(data) {
  return request({
    url: '/api/v1/t-carp-rods',
    method: 'delete',
    data: data
  })
}

