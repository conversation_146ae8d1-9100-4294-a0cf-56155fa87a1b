import request from '@/utils/request'

// 查询TTackleBoxes列表
export function listTTackleBoxes(query) {
  return request({
    url: '/api/v1/t-tackle-boxes',
    method: 'get',
    params: query
  })
}

// 查询TTackleBoxes详细
export function getTTackleBoxes(id) {
  return request({
    url: '/api/v1/t-tackle-boxes/' + id,
    method: 'get'
  })
}

// 新增TTackleBoxes
export function addTTackleBoxes(data) {
  return request({
    url: '/api/v1/t-tackle-boxes',
    method: 'post',
    data: data
  })
}

// 修改TTackleBoxes
export function updateTTackleBoxes(data) {
  return request({
    url: '/api/v1/t-tackle-boxes/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TTackleBoxes
export function delTTackleBoxes(data) {
  return request({
    url: '/api/v1/t-tackle-boxes',
    method: 'delete',
    data: data
  })
}

