import request from '@/utils/request'

// 查询TCastingRods列表
export function listTCastingRods(query) {
  return request({
    url: '/api/v1/t-casting-rods',
    method: 'get',
    params: query
  })
}

// 查询TCastingRods详细
export function getTCastingRods(id) {
  return request({
    url: '/api/v1/t-casting-rods/' + id,
    method: 'get'
  })
}

// 新增TCastingRods
export function addTCastingRods(data) {
  return request({
    url: '/api/v1/t-casting-rods',
    method: 'post',
    data: data
  })
}

// 修改TCastingRods
export function updateTCastingRods(data) {
  return request({
    url: '/api/v1/t-casting-rods/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TCastingRods
export function delTCastingRods(data) {
  return request({
    url: '/api/v1/t-casting-rods',
    method: 'delete',
    data: data
  })
}

