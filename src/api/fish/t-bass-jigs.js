import request from '@/utils/request'

// 查询TBassJigs列表
export function listTBassJigs(query) {
  return request({
    url: '/api/v1/t-bass-jigs',
    method: 'get',
    params: query
  })
}

// 查询TBassJigs详细
export function getTBassJigs(id) {
  return request({
    url: '/api/v1/t-bass-jigs/' + id,
    method: 'get'
  })
}

// 新增TBassJigs
export function addTBassJigs(data) {
  return request({
    url: '/api/v1/t-bass-jigs',
    method: 'post',
    data: data
  })
}

// 修改TBassJigs
export function updateTBassJigs(data) {
  return request({
    url: '/api/v1/t-bass-jigs/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TBassJigs
export function delTBassJigs(data) {
  return request({
    url: '/api/v1/t-bass-jigs',
    method: 'delete',
    data: data
  })
}

