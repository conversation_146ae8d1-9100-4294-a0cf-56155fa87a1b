import request from '@/utils/request'

// 查询TSoftPlasticBaits列表
export function listTSoftPlasticBaits(query) {
  return request({
    url: '/api/v1/t-soft-plastic-baits',
    method: 'get',
    params: query
  })
}

// 查询TSoftPlasticBaits详细
export function getTSoftPlasticBaits(id) {
  return request({
    url: '/api/v1/t-soft-plastic-baits/' + id,
    method: 'get'
  })
}

// 新增TSoftPlasticBaits
export function addTSoftPlasticBaits(data) {
  return request({
    url: '/api/v1/t-soft-plastic-baits',
    method: 'post',
    data: data
  })
}

// 修改TSoftPlasticBaits
export function updateTSoftPlasticBaits(data) {
  return request({
    url: '/api/v1/t-soft-plastic-baits/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TSoftPlasticBaits
export function delTSoftPlasticBaits(data) {
  return request({
    url: '/api/v1/t-soft-plastic-baits',
    method: 'delete',
    data: data
  })
}

