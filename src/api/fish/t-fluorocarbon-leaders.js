import request from '@/utils/request'

// 查询TFluorocarbonLeaders列表
export function listTFluorocarbonLeaders(query) {
  return request({
    url: '/api/v1/t-fluorocarbon-leaders',
    method: 'get',
    params: query
  })
}

// 查询TFluorocarbonLeaders详细
export function getTFluorocarbonLeaders(id) {
  return request({
    url: '/api/v1/t-fluorocarbon-leaders/' + id,
    method: 'get'
  })
}

// 新增TFluorocarbonLeaders
export function addTFluorocarbonLeaders(data) {
  return request({
    url: '/api/v1/t-fluorocarbon-leaders',
    method: 'post',
    data: data
  })
}

// 修改TFluorocarbonLeaders
export function updateTFluorocarbonLeaders(data) {
  return request({
    url: '/api/v1/t-fluorocarbon-leaders/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TFluorocarbonLeaders
export function delTFluorocarbonLeaders(data) {
  return request({
    url: '/api/v1/t-fluorocarbon-leaders',
    method: 'delete',
    data: data
  })
}

