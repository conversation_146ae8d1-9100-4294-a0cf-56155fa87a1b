import request from '@/utils/request'

// 查询TBottomRods列表
export function listTBottomRods(query) {
  return request({
    url: '/api/v1/t-bottom-rods',
    method: 'get',
    params: query
  })
}

// 查询TBottomRods详细
export function getTBottomRods(id) {
  return request({
    url: '/api/v1/t-bottom-rods/' + id,
    method: 'get'
  })
}

// 新增TBottomRods
export function addTBottomRods(data) {
  return request({
    url: '/api/v1/t-bottom-rods',
    method: 'post',
    data: data
  })
}

// 修改TBottomRods
export function updateTBottomRods(data) {
  return request({
    url: '/api/v1/t-bottom-rods/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TBottomRods
export function delTBottomRods(data) {
  return request({
    url: '/api/v1/t-bottom-rods',
    method: 'delete',
    data: data
  })
}

