import request from '@/utils/request'

// 查询TJigHeads列表
export function listTJigHeads(query) {
  return request({
    url: '/api/v1/t-jig-heads',
    method: 'get',
    params: query
  })
}

// 查询TJigHeads详细
export function getTJigHeads(id) {
  return request({
    url: '/api/v1/t-jig-heads/' + id,
    method: 'get'
  })
}

// 新增TJigHeads
export function addTJigHeads(data) {
  return request({
    url: '/api/v1/t-jig-heads',
    method: 'post',
    data: data
  })
}

// 修改TJigHeads
export function updateTJigHeads(data) {
  return request({
    url: '/api/v1/t-jig-heads/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TJigHeads
export function delTJigHeads(data) {
  return request({
    url: '/api/v1/t-jig-heads',
    method: 'delete',
    data: data
  })
}

