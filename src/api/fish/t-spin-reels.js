import request from '@/utils/request'

// 查询TSpinReels列表
export function listTSpinReels(query) {
  return request({
    url: '/api/v1/t-spin-reels',
    method: 'get',
    params: query
  })
}

// 查询TSpinReels详细
export function getTSpinReels(id) {
  return request({
    url: '/api/v1/t-spin-reels/' + id,
    method: 'get'
  })
}

// 新增TSpinReels
export function addTSpinReels(data) {
  return request({
    url: '/api/v1/t-spin-reels',
    method: 'post',
    data: data
  })
}

// 修改TSpinReels
export function updateTSpinReels(data) {
  return request({
    url: '/api/v1/t-spin-reels/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TSpinReels
export function delTSpinReels(data) {
  return request({
    url: '/api/v1/t-spin-reels',
    method: 'delete',
    data: data
  })
}

