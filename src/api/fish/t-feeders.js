import request from '@/utils/request'

// 查询TFeeders列表
export function listTFeeders(query) {
  return request({
    url: '/api/v1/t-feeders',
    method: 'get',
    params: query
  })
}

// 查询TFeeders详细
export function getTFeeders(id) {
  return request({
    url: '/api/v1/t-feeders/' + id,
    method: 'get'
  })
}

// 新增TFeeders
export function addTFeeders(data) {
  return request({
    url: '/api/v1/t-feeders',
    method: 'post',
    data: data
  })
}

// 修改TFeeders
export function updateTFeeders(data) {
  return request({
    url: '/api/v1/t-feeders/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TFeeders
export function delTFeeders(data) {
  return request({
    url: '/api/v1/t-feeders',
    method: 'delete',
    data: data
  })
}

