import request from '@/utils/request'

// 查询TMatchRods列表
export function listTMatchRods(query) {
  return request({
    url: '/api/v1/t-match-rods',
    method: 'get',
    params: query
  })
}

// 查询TMatchRods详细
export function getTMatchRods(id) {
  return request({
    url: '/api/v1/t-match-rods/' + id,
    method: 'get'
  })
}

// 新增TMatchRods
export function addTMatchRods(data) {
  return request({
    url: '/api/v1/t-match-rods',
    method: 'post',
    data: data
  })
}

// 修改TMatchRods
export function updateTMatchRods(data) {
  return request({
    url: '/api/v1/t-match-rods/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TMatchRods
export function delTMatchRods(data) {
  return request({
    url: '/api/v1/t-match-rods',
    method: 'delete',
    data: data
  })
}

