import request from '@/utils/request'

// 查询TSpinners列表
export function listTSpinners(query) {
  return request({
    url: '/api/v1/t-spinners',
    method: 'get',
    params: query
  })
}

// 查询TSpinners详细
export function getTSpinners(id) {
  return request({
    url: '/api/v1/t-spinners/' + id,
    method: 'get'
  })
}

// 新增TSpinners
export function addTSpinners(data) {
  return request({
    url: '/api/v1/t-spinners',
    method: 'post',
    data: data
  })
}

// 修改TSpinners
export function updateTSpinners(data) {
  return request({
    url: '/api/v1/t-spinners/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TSpinners
export function delTSpinners(data) {
  return request({
    url: '/api/v1/t-spinners',
    method: 'delete',
    data: data
  })
}

