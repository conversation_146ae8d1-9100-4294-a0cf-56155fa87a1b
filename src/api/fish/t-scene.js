import request from '@/utils/request'

// 查询TScene列表
export function listTScene(query) {
  return request({
    url: '/api/v1/t-scene',
    method: 'get',
    params: query
  })
}

// 查询TScene详细
export function getTScene(id) {
  return request({
    url: '/api/v1/t-scene/' + id,
    method: 'get'
  })
}

// 新增TScene
export function addTScene(data) {
  return request({
    url: '/api/v1/t-scene',
    method: 'post',
    data: data
  })
}

// 修改TScene
export function updateTScene(data) {
  return request({
    url: '/api/v1/t-scene/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TScene
export function delTScene(data) {
  return request({
    url: '/api/v1/t-scene',
    method: 'delete',
    data: data
  })
}

