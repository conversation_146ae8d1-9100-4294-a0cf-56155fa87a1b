import request from '@/utils/request'

// 查询TFlashlights列表
export function listTFlashlights(query) {
  return request({
    url: '/api/v1/t-flashlights',
    method: 'get',
    params: query
  })
}

// 查询TFlashlights详细
export function getTFlashlights(id) {
  return request({
    url: '/api/v1/t-flashlights/' + id,
    method: 'get'
  })
}

// 新增TFlashlights
export function addTFlashlights(data) {
  return request({
    url: '/api/v1/t-flashlights',
    method: 'post',
    data: data
  })
}

// 修改TFlashlights
export function updateTFlashlights(data) {
  return request({
    url: '/api/v1/t-flashlights/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TFlashlights
export function delTFlashlights(data) {
  return request({
    url: '/api/v1/t-flashlights',
    method: 'delete',
    data: data
  })
}

