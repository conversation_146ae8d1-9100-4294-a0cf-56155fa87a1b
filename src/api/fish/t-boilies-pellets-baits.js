import request from '@/utils/request'

// 查询TBoiliesPelletsBaits列表
export function listTBoiliesPelletsBaits(query) {
  return request({
    url: '/api/v1/t-boilies-pellets-baits',
    method: 'get',
    params: query
  })
}

// 查询TBoiliesPelletsBaits详细
export function getTBoiliesPelletsBaits(id) {
  return request({
    url: '/api/v1/t-boilies-pellets-baits/' + id,
    method: 'get'
  })
}

// 新增TBoiliesPelletsBaits
export function addTBoiliesPelletsBaits(data) {
  return request({
    url: '/api/v1/t-boilies-pellets-baits',
    method: 'post',
    data: data
  })
}

// 修改TBoiliesPelletsBaits
export function updateTBoiliesPelletsBaits(data) {
  return request({
    url: '/api/v1/t-boilies-pellets-baits/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TBoiliesPelletsBaits
export function delTBoiliesPelletsBaits(data) {
  return request({
    url: '/api/v1/t-boilies-pellets-baits',
    method: 'delete',
    data: data
  })
}

