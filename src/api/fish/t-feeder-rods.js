import request from '@/utils/request'

// 查询TFeederRods列表
export function listTFeederRods(query) {
  return request({
    url: '/api/v1/t-feeder-rods',
    method: 'get',
    params: query
  })
}

// 查询TFeederRods详细
export function getTFeederRods(id) {
  return request({
    url: '/api/v1/t-feeder-rods/' + id,
    method: 'get'
  })
}

// 新增TFeederRods
export function addTFeederRods(data) {
  return request({
    url: '/api/v1/t-feeder-rods',
    method: 'post',
    data: data
  })
}

// 修改TFeederRods
export function updateTFeederRods(data) {
  return request({
    url: '/api/v1/t-feeder-rods/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TFeederRods
export function delTFeederRods(data) {
  return request({
    url: '/api/v1/t-feeder-rods',
    method: 'delete',
    data: data
  })
}

