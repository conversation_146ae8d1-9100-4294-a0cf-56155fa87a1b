import request from '@/utils/request'

// 查询TFluorocarbonLines列表
export function listTFluorocarbonLines(query) {
  return request({
    url: '/api/v1/t-fluorocarbon-lines',
    method: 'get',
    params: query
  })
}

// 查询TFluorocarbonLines详细
export function getTFluorocarbonLines(id) {
  return request({
    url: '/api/v1/t-fluorocarbon-lines/' + id,
    method: 'get'
  })
}

// 新增TFluorocarbonLines
export function addTFluorocarbonLines(data) {
  return request({
    url: '/api/v1/t-fluorocarbon-lines',
    method: 'post',
    data: data
  })
}

// 修改TFluorocarbonLines
export function updateTFluorocarbonLines(data) {
  return request({
    url: '/api/v1/t-fluorocarbon-lines/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TFluorocarbonLines
export function delTFluorocarbonLines(data) {
  return request({
    url: '/api/v1/t-fluorocarbon-lines',
    method: 'delete',
    data: data
  })
}

