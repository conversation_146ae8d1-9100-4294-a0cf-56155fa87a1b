import request from '@/utils/request'

// 查询TSpodRods列表
export function listTSpodRods(query) {
  return request({
    url: '/api/v1/t-spod-rods',
    method: 'get',
    params: query
  })
}

// 查询TSpodRods详细
export function getTSpodRods(id) {
  return request({
    url: '/api/v1/t-spod-rods/' + id,
    method: 'get'
  })
}

// 新增TSpodRods
export function addTSpodRods(data) {
  return request({
    url: '/api/v1/t-spod-rods',
    method: 'post',
    data: data
  })
}

// 修改TSpodRods
export function updateTSpodRods(data) {
  return request({
    url: '/api/v1/t-spod-rods/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TSpodRods
export function delTSpodRods(data) {
  return request({
    url: '/api/v1/t-spod-rods',
    method: 'delete',
    data: data
  })
}

