import request from '@/utils/request'

// 查询TBraidedLines列表
export function listTBraidedLines(query) {
  return request({
    url: '/api/v1/t-braided-lines',
    method: 'get',
    params: query
  })
}

// 查询TBraidedLines详细
export function getTBraidedLines(id) {
  return request({
    url: '/api/v1/t-braided-lines/' + id,
    method: 'get'
  })
}

// 新增TBraidedLines
export function addTBraidedLines(data) {
  return request({
    url: '/api/v1/t-braided-lines',
    method: 'post',
    data: data
  })
}

// 修改TBraidedLines
export function updateTBraidedLines(data) {
  return request({
    url: '/api/v1/t-braided-lines/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TBraidedLines
export function delTBraidedLines(data) {
  return request({
    url: '/api/v1/t-braided-lines',
    method: 'delete',
    data: data
  })
}

