import request from '@/utils/request'

// 查询TSinkers列表
export function listTSinkers(query) {
  return request({
    url: '/api/v1/t-sinkers',
    method: 'get',
    params: query
  })
}

// 查询TSinkers详细
export function getTSinkers(id) {
  return request({
    url: '/api/v1/t-sinkers/' + id,
    method: 'get'
  })
}

// 新增TSinkers
export function addTSinkers(data) {
  return request({
    url: '/api/v1/t-sinkers',
    method: 'post',
    data: data
  })
}

// 修改TSinkers
export function updateTSinkers(data) {
  return request({
    url: '/api/v1/t-sinkers/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TSinkers
export function delTSinkers(data) {
  return request({
    url: '/api/v1/t-sinkers',
    method: 'delete',
    data: data
  })
}

