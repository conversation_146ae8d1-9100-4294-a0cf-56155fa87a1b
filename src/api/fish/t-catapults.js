import request from '@/utils/request'

// 查询TCatapults列表
export function listTCatapults(query) {
  return request({
    url: '/api/v1/t-catapults',
    method: 'get',
    params: query
  })
}

// 查询TCatapults详细
export function getTCatapults(id) {
  return request({
    url: '/api/v1/t-catapults/' + id,
    method: 'get'
  })
}

// 新增TCatapults
export function addTCatapults(data) {
  return request({
    url: '/api/v1/t-catapults',
    method: 'post',
    data: data
  })
}

// 修改TCatapults
export function updateTCatapults(data) {
  return request({
    url: '/api/v1/t-catapults/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TCatapults
export function delTCatapults(data) {
  return request({
    url: '/api/v1/t-catapults',
    method: 'delete',
    data: data
  })
}

