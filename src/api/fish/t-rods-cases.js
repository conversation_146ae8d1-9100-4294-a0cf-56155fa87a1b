import request from '@/utils/request'

// 查询TRodsCases列表
export function listTRodsCases(query) {
  return request({
    url: '/api/v1/t-rods-cases',
    method: 'get',
    params: query
  })
}

// 查询TRodsCases详细
export function getTRodsCases(id) {
  return request({
    url: '/api/v1/t-rods-cases/' + id,
    method: 'get'
  })
}

// 新增TRodsCases
export function addTRodsCases(data) {
  return request({
    url: '/api/v1/t-rods-cases',
    method: 'post',
    data: data
  })
}

// 修改TRodsCases
export function updateTRodsCases(data) {
  return request({
    url: '/api/v1/t-rods-cases/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TRodsCases
export function delTRodsCases(data) {
  return request({
    url: '/api/v1/t-rods-cases',
    method: 'delete',
    data: data
  })
}

