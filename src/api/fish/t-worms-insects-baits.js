import request from '@/utils/request'

// 查询TWormsInsectsBaits列表
export function listTWormsInsectsBaits(query) {
  return request({
    url: '/api/v1/t-worms-insects-baits',
    method: 'get',
    params: query
  })
}

// 查询TWormsInsectsBaits详细
export function getTWormsInsectsBaits(id) {
  return request({
    url: '/api/v1/t-worms-insects-baits/' + id,
    method: 'get'
  })
}

// 新增TWormsInsectsBaits
export function addTWormsInsectsBaits(data) {
  return request({
    url: '/api/v1/t-worms-insects-baits',
    method: 'post',
    data: data
  })
}

// 修改TWormsInsectsBaits
export function updateTWormsInsectsBaits(data) {
  return request({
    url: '/api/v1/t-worms-insects-baits/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TWormsInsectsBaits
export function delTWormsInsectsBaits(data) {
  return request({
    url: '/api/v1/t-worms-insects-baits',
    method: 'delete',
    data: data
  })
}

