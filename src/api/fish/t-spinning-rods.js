import request from '@/utils/request'

// 查询TSpinningRods列表
export function listTSpinningRods(query) {
  return request({
    url: '/api/v1/t-spinning-rods',
    method: 'get',
    params: query
  })
}

// 查询TSpinningRods详细
export function getTSpinningRods(id) {
  return request({
    url: '/api/v1/t-spinning-rods/' + id,
    method: 'get'
  })
}

// 新增TSpinningRods
export function addTSpinningRods(data) {
  return request({
    url: '/api/v1/t-spinning-rods',
    method: 'post',
    data: data
  })
}

// 修改TSpinningRods
export function updateTSpinningRods(data) {
  return request({
    url: '/api/v1/t-spinning-rods/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TSpinningRods
export function delTSpinningRods(data) {
  return request({
    url: '/api/v1/t-spinning-rods',
    method: 'delete',
    data: data
  })
}

