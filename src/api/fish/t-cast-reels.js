import request from '@/utils/request'

// 查询TCastReels列表
export function listTCastReels(query) {
  return request({
    url: '/api/v1/t-cast-reels',
    method: 'get',
    params: query
  })
}

// 查询TCastReels详细
export function getTCastReels(id) {
  return request({
    url: '/api/v1/t-cast-reels/' + id,
    method: 'get'
  })
}

// 新增TCastReels
export function addTCastReels(data) {
  return request({
    url: '/api/v1/t-cast-reels',
    method: 'post',
    data: data
  })
}

// 修改TCastReels
export function updateTCastReels(data) {
  return request({
    url: '/api/v1/t-cast-reels/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TCastReels
export function delTCastReels(data) {
  return request({
    url: '/api/v1/t-cast-reels',
    method: 'delete',
    data: data
  })
}

