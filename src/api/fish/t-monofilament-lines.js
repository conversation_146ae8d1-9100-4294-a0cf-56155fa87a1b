import request from '@/utils/request'

// 查询TMonofilamentLines列表
export function listTMonofilamentLines(query) {
  return request({
    url: '/api/v1/t-monofilament-lines',
    method: 'get',
    params: query
  })
}

// 查询TMonofilamentLines详细
export function getTMonofilamentLines(id) {
  return request({
    url: '/api/v1/t-monofilament-lines/' + id,
    method: 'get'
  })
}

// 新增TMonofilamentLines
export function addTMonofilamentLines(data) {
  return request({
    url: '/api/v1/t-monofilament-lines',
    method: 'post',
    data: data
  })
}

// 修改TMonofilamentLines
export function updateTMonofilamentLines(data) {
  return request({
    url: '/api/v1/t-monofilament-lines/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TMonofilamentLines
export function delTMonofilamentLines(data) {
  return request({
    url: '/api/v1/t-monofilament-lines',
    method: 'delete',
    data: data
  })
}

