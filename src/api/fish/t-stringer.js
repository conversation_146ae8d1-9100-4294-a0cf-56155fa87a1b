import request from '@/utils/request'

// 查询TStringer列表
export function listTStringer(query) {
  return request({
    url: '/api/v1/t-stringer',
    method: 'get',
    params: query
  })
}

// 查询TStringer详细
export function getTStringer(id) {
  return request({
    url: '/api/v1/t-stringer/' + id,
    method: 'get'
  })
}

// 新增TStringer
export function addTStringer(data) {
  return request({
    url: '/api/v1/t-stringer',
    method: 'post',
    data: data
  })
}

// 修改TStringer
export function updateTStringer(data) {
  return request({
    url: '/api/v1/t-stringer/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TStringer
export function delTStringer(data) {
  return request({
    url: '/api/v1/t-stringer',
    method: 'delete',
    data: data
  })
}

