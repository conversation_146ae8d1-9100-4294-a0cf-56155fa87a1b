import request from '@/utils/request'

// 查询TTitaniumLeaders列表
export function listTTitaniumLeaders(query) {
  return request({
    url: '/api/v1/t-titanium-leaders',
    method: 'get',
    params: query
  })
}

// 查询TTitaniumLeaders详细
export function getTTitaniumLeaders(id) {
  return request({
    url: '/api/v1/t-titanium-leaders/' + id,
    method: 'get'
  })
}

// 新增TTitaniumLeaders
export function addTTitaniumLeaders(data) {
  return request({
    url: '/api/v1/t-titanium-leaders',
    method: 'post',
    data: data
  })
}

// 修改TTitaniumLeaders
export function updateTTitaniumLeaders(data) {
  return request({
    url: '/api/v1/t-titanium-leaders/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TTitaniumLeaders
export function delTTitaniumLeaders(data) {
  return request({
    url: '/api/v1/t-titanium-leaders',
    method: 'delete',
    data: data
  })
}

