import request from '@/utils/request'

// 查询TFishInformation列表
export function listTFishInformation(query) {
  return request({
    url: '/api/v1/t-fish-information',
    method: 'get',
    params: query
  })
}

// 查询TFishInformation详细
export function getTFishInformation(id) {
  return request({
    url: '/api/v1/t-fish-information/' + id,
    method: 'get'
  })
}

// 新增TFishInformation
export function addTFishInformation(data) {
  return request({
    url: '/api/v1/t-fish-information',
    method: 'post',
    data: data
  })
}

// 修改TFishInformation
export function updateTFishInformation(data) {
  return request({
    url: '/api/v1/t-fish-information/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TFishInformation
export function delTFishInformation(data) {
  return request({
    url: '/api/v1/t-fish-information',
    method: 'delete',
    data: data
  })
}

