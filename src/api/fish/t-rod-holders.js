import request from '@/utils/request'

// 查询TRodHolders列表
export function listTRodHolders(query) {
  return request({
    url: '/api/v1/t-rod-holders',
    method: 'get',
    params: query
  })
}

// 查询TRodHolders详细
export function getTRodHolders(id) {
  return request({
    url: '/api/v1/t-rod-holders/' + id,
    method: 'get'
  })
}

// 新增TRodHolders
export function addTRodHolders(data) {
  return request({
    url: '/api/v1/t-rod-holders',
    method: 'post',
    data: data
  })
}

// 修改TRodHolders
export function updateTRodHolders(data) {
  return request({
    url: '/api/v1/t-rod-holders/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TRodHolders
export function delTRodHolders(data) {
  return request({
    url: '/api/v1/t-rod-holders',
    method: 'delete',
    data: data
  })
}

