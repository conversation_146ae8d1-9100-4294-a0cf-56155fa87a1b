import request from '@/utils/request'

// 查询TCommonBaits列表
export function listTCommonBaits(query) {
  return request({
    url: '/api/v1/t-common-baits',
    method: 'get',
    params: query
  })
}

// 查询TCommonBaits详细
export function getTCommonBaits(id) {
  return request({
    url: '/api/v1/t-common-baits/' + id,
    method: 'get'
  })
}

// 新增TCommonBaits
export function addTCommonBaits(data) {
  return request({
    url: '/api/v1/t-common-baits',
    method: 'post',
    data: data
  })
}

// 修改TCommonBaits
export function updateTCommonBaits(data) {
  return request({
    url: '/api/v1/t-common-baits/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TCommonBaits
export function delTCommonBaits(data) {
  return request({
    url: '/api/v1/t-common-baits',
    method: 'delete',
    data: data
  })
}

