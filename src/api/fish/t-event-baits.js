import request from '@/utils/request'

// 查询TEventBaits列表
export function listTEventBaits(query) {
  return request({
    url: '/api/v1/t-event-baits',
    method: 'get',
    params: query
  })
}

// 查询TEventBaits详细
export function getTEventBaits(id) {
  return request({
    url: '/api/v1/t-event-baits/' + id,
    method: 'get'
  })
}

// 新增TEventBaits
export function addTEventBaits(data) {
  return request({
    url: '/api/v1/t-event-baits',
    method: 'post',
    data: data
  })
}

// 修改TEventBaits
export function updateTEventBaits(data) {
  return request({
    url: '/api/v1/t-event-baits/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TEventBaits
export function delTEventBaits(data) {
  return request({
    url: '/api/v1/t-event-baits',
    method: 'delete',
    data: data
  })
}

