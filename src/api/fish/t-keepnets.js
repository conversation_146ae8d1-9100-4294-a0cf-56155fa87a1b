import request from '@/utils/request'

// 查询TKeepnets列表
export function listTKeepnets(query) {
  return request({
    url: '/api/v1/t-keepnets',
    method: 'get',
    params: query
  })
}

// 查询TKeepnets详细
export function getTKeepnets(id) {
  return request({
    url: '/api/v1/t-keepnets/' + id,
    method: 'get'
  })
}

// 新增TKeepnets
export function addTKeepnets(data) {
  return request({
    url: '/api/v1/t-keepnets',
    method: 'post',
    data: data
  })
}

// 修改TKeepnets
export function updateTKeepnets(data) {
  return request({
    url: '/api/v1/t-keepnets/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TKeepnets
export function delTKeepnets(data) {
  return request({
    url: '/api/v1/t-keepnets',
    method: 'delete',
    data: data
  })
}

