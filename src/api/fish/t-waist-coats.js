import request from '@/utils/request'

// 查询TWaistCoats列表
export function listTWaistCoats(query) {
  return request({
    url: '/api/v1/t-waist-coats',
    method: 'get',
    params: query
  })
}

// 查询TWaistCoats详细
export function getTWaistCoats(id) {
  return request({
    url: '/api/v1/t-waist-coats/' + id,
    method: 'get'
  })
}

// 新增TWaistCoats
export function addTWaistCoats(data) {
  return request({
    url: '/api/v1/t-waist-coats',
    method: 'post',
    data: data
  })
}

// 修改TWaistCoats
export function updateTWaistCoats(data) {
  return request({
    url: '/api/v1/t-waist-coats/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TWaistCoats
export function delTWaistCoats(data) {
  return request({
    url: '/api/v1/t-waist-coats',
    method: 'delete',
    data: data
  })
}

