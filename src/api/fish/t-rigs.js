import request from '@/utils/request'

// 查询TRigs列表
export function listTRigs(query) {
  return request({
    url: '/api/v1/t-rigs',
    method: 'get',
    params: query
  })
}

// 查询TRigs详细
export function getTRigs(id) {
  return request({
    url: '/api/v1/t-rigs/' + id,
    method: 'get'
  })
}

// 新增TRigs
export function addTRigs(data) {
  return request({
    url: '/api/v1/t-rigs',
    method: 'post',
    data: data
  })
}

// 修改TRigs
export function updateTRigs(data) {
  return request({
    url: '/api/v1/t-rigs/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除TRigs
export function delTRigs(data) {
  return request({
    url: '/api/v1/t-rigs',
    method: 'delete',
    data: data
  })
}

